#!/usr/bin/env python3
"""
إصلاح مشاكل النظام
"""

import os
import sqlite3
import json
import shutil
from datetime import datetime

def fix_api_keys_database():
    """إصلاح قاعدة بيانات مفاتيح API"""
    print("🔧 إصلاح قاعدة بيانات مفاتيح API...")
    
    try:
        # إنشاء نسخة احتياطية إذا كانت موجودة
        db_path = "data/api_keys.db"
        if os.path.exists(db_path):
            backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(db_path, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # إنشاء قاعدة بيانات جديدة بالبنية الصحيحة
        os.makedirs("data", exist_ok=True)
        
        with sqlite3.connect(db_path) as conn:
            # حذف الجدول القديم إذا كان موجوداً
            conn.execute("DROP TABLE IF EXISTS api_keys")
            
            # إنشاء جدول جديد بالبنية الصحيحة
            conn.execute("""
                CREATE TABLE api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    key TEXT NOT NULL,
                    service TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    last_used TEXT,
                    usage_count INTEGER DEFAULT 0,
                    description TEXT
                )
            """)
            
            # إنشاء جدول سجل الاستخدام
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_usage_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_key_id INTEGER,
                    service TEXT,
                    endpoint TEXT,
                    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN,
                    error_message TEXT,
                    FOREIGN KEY (api_key_id) REFERENCES api_keys (id)
                )
            """)
            
            print("✅ تم إنشاء قاعدة بيانات مفاتيح API بالبنية الصحيحة")
            
            # استيراد المفاتيح من .env
            import_env_keys(conn)
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")

def import_env_keys(conn):
    """استيراد المفاتيح من ملف .env"""
    print("📥 استيراد المفاتيح من ملف .env...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        # قائمة المفاتيح المهمة من .env
        env_keys = {
            "GEMINI_API_KEY": "Google Gemini",
            "TELEGRAM_BOT_TOKEN": "Telegram Bot",
            "BLOGGER_CLIENT_ID": "Google Blogger",
            "BLOGGER_CLIENT_SECRET": "Google Blogger Secret",
            "FREEPIK_API_KEY": "Freepik",
            "FLUXAI_API_KEY": "FluxAI",
            "RAWG_API_KEY": "RAWG Gaming",
            "APIFY_API_TOKEN": "Apify",
            "SEARCH1API_KEY_1": "Search1API",
            "SEARCH1API_KEY_2": "Search1API",
            "SEARCH1API_KEY_3": "Search1API",
            "TAVILY_API_KEY_1": "Tavily Search",
            "TAVILY_API_KEY_2": "Tavily Search",
            "SERPAPI_KEY_1": "SerpAPI",
            "ASSEMBLYAI_API_KEY_1": "AssemblyAI",
            "ASSEMBLYAI_API_KEY_2": "AssemblyAI",
            "WIT_AI_ACCESS_TOKEN_1": "Wit.ai",
            "YOUTUBE_DATA_API_KEY_1": "YouTube Data",
            "PEXELS_API_KEY_1": "Pexels Images"
        }
        
        imported_count = 0
        for env_key, service in env_keys.items():
            key_value = os.getenv(env_key)
            if key_value and key_value != "your_key_here":
                try:
                    conn.execute("""
                        INSERT INTO api_keys (name, key, service, description)
                        VALUES (?, ?, ?, ?)
                    """, (env_key, key_value, service, f"مستورد من ملف .env"))
                    imported_count += 1
                except Exception as e:
                    print(f"⚠️ خطأ في استيراد {env_key}: {e}")
        
        print(f"✅ تم استيراد {imported_count} مفتاح من ملف .env")
        
    except Exception as e:
        print(f"❌ خطأ في استيراد المفاتيح: {e}")

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    print("📁 إنشاء الملفات المفقودة...")
    
    # إنشاء ملف client_secret.json فارغ
    client_secret_path = "client_secret.json"
    if not os.path.exists(client_secret_path):
        client_secret_template = {
            "installed": *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        }
        
        with open(client_secret_path, 'w', encoding='utf-8') as f:
            json.dump(client_secret_template, f, indent=2)
        
        print(f"✅ تم إنشاء {client_secret_path}")
    
    # إنشاء ملف service_account.json فارغ
    service_account_path = "service_account.json"
    if not os.path.exists(service_account_path):
        service_account_template = *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        
        with open(service_account_path, 'w', encoding='utf-8') as f:
            json.dump(service_account_template, f, indent=2)
        
        print(f"✅ تم إنشاء {service_account_path}")
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ["data", "logs", "config", "cache", "images", "backups"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            print(f"✅ تم إنشاء مجلد: {dir_name}")

def fix_telegram_api_manager():
    """إصلاح مدير API تيليجرام"""
    print("🔧 إصلاح مدير API تيليجرام...")
    
    try:
        # إعادة تهيئة قاعدة البيانات
        from modules.telegram_api_manager import telegram_api_manager
        
        # إعادة تهيئة قاعدة البيانات
        telegram_api_manager.init_database()
        
        print("✅ تم إصلاح مدير API تيليجرام")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح مدير API تيليجرام: {e}")

def test_system_after_fix():
    """اختبار النظام بعد الإصلاح"""
    print("\n🧪 اختبار النظام بعد الإصلاح...")
    
    try:
        # اختبار استيراد الوحدات
        from modules.telegram_api_manager import telegram_api_manager
        print("✅ تم استيراد telegram_api_manager")
        
        # اختبار قاعدة البيانات
        keys = telegram_api_manager.get_all_keys()
        print(f"✅ قاعدة البيانات تعمل - عدد المفاتيح: {len(keys)}")
        
        # اختبار الإحصائيات
        stats = telegram_api_manager.get_usage_stats()
        print(f"✅ الإحصائيات تعمل - إجمالي المفاتيح: {stats.get('total_keys', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🔧 بدء إصلاح مشاكل النظام")
    print("=" * 50)
    
    # إصلاح قاعدة بيانات مفاتيح API
    fix_api_keys_database()
    
    # إنشاء الملفات المفقودة
    create_missing_files()
    
    # إصلاح مدير API تيليجرام
    fix_telegram_api_manager()
    
    # اختبار النظام
    if test_system_after_fix():
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("\n🚀 يمكنك الآن تشغيل النظام:")
        print("python main_bot_only.py")
    else:
        print("\n❌ لا تزال هناك مشاكل في النظام")
    
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح: {e}")
        import traceback
        traceback.print_exc()
