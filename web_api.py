#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم واجهة الويب لوكيل أخبار الألعاب
Web API Server for Gaming News Agent
"""

import os
import sys
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import asyncio
import sqlite3

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد Flask
try:
    from flask import Flask, render_template, jsonify, request, send_from_directory
    from flask_cors import CORS
except ImportError:
    print("❌ Flask غير مثبت. يرجى تثبيته باستخدام: pip install flask flask-cors")
    sys.exit(1)

# استيراد الوحدات المحلية
try:
    from modules.logger import logger
    from modules.database import db
    from modules.web_approval_system import web_approval_system
    from config.settings import BotConfig
except ImportError as e:
    print(f"⚠️ تحذير: لم يتم العثور على بعض الوحدات: {e}")
    # إنشاء logger بديل
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

# إنشاء تطبيق Flask محسن للوضع الخفيف
app = Flask(__name__,
           static_folder='web_interface',
           template_folder='web_interface')

# تفعيل CORS للسماح بطلبات من المتصفح
CORS(app)

# إعدادات التطبيق محسنة للذاكرة
app.config['SECRET_KEY'] = 'gaming_news_agent_2025'
app.config['JSON_AS_ASCII'] = False  # دعم النصوص العربية
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # حد أقصى 16MB

# تحسينات للوضع الخفيف
if os.getenv('LITE_MODE', 'false').lower() == 'true':
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # كاش لمدة سنة
    app.config['TEMPLATES_AUTO_RELOAD'] = False  # تعطيل إعادة تحميل القوالب

class WebAPIManager:
    """مدير واجهة الويب"""
    
    def __init__(self):
        self.bot_status = "offline"
        self.start_time = datetime.now()
        self.stats = {
            'articles_published': 0,
            'content_processed': 0,
            'success_rate': 0,
            'uptime': '00:00:00'
        }
        self.settings = self.load_settings()
        
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            # محاولة تحميل إعدادات البوت
            if os.path.exists('config/bot_config.json'):
                with open('config/bot_config.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    'telegram_enabled': False,
                    'blogger_enabled': True,
                    'auto_approval': True,
                    'content_interval': 30,
                    'max_articles_per_day': 10
                }
        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")
            return {}
    
    def get_database_stats(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if os.path.exists('data/articles.db'):
                conn = sqlite3.connect('data/articles.db')
                cursor = conn.cursor()
                
                # عدد المقالات المنشورة اليوم
                today = datetime.now().strftime('%Y-%m-%d')
                cursor.execute("""
                    SELECT COUNT(*) FROM articles 
                    WHERE DATE(created_at) = ? AND status = 'published'
                """, (today,))
                articles_today = cursor.fetchone()[0]
                
                # إجمالي المقالات
                cursor.execute("SELECT COUNT(*) FROM articles")
                total_articles = cursor.fetchone()[0]
                
                conn.close()
                
                return {
                    'articles_today': articles_today,
                    'total_articles': total_articles
                }
            else:
                return {'articles_today': 0, 'total_articles': 0}
        except Exception as e:
            logger.error(f"خطأ في قراءة قاعدة البيانات: {e}")
            return {'articles_today': 0, 'total_articles': 0}
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        db_stats = self.get_database_stats()
        uptime = datetime.now() - self.start_time
        
        self.stats.update({
            'articles_published': db_stats['articles_today'],
            'content_processed': db_stats['total_articles'],
            'success_rate': 95,  # قيمة افتراضية
            'uptime': str(uptime).split('.')[0]  # إزالة الميكروثواني
        })

# إنشاء مدير الواجهة
web_manager = WebAPIManager()

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return send_from_directory('web_interface', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """ملفات الواجهة الثابتة"""
    return send_from_directory('web_interface', filename)

# API endpoints
@app.route('/api/status')
def get_status():
    """الحصول على حالة النظام"""
    web_manager.update_stats()
    
    return jsonify({
        'success': True,
        'timestamp': datetime.now().isoformat(),
        'bot_status': web_manager.bot_status,
        'stats': web_manager.stats,
        'uptime_seconds': int((datetime.now() - web_manager.start_time).total_seconds())
    })

@app.route('/api/settings')
def get_settings():
    """الحصول على الإعدادات"""
    return jsonify({
        'success': True,
        'settings': web_manager.settings
    })

@app.route('/api/settings', methods=['POST'])
def update_settings():
    """تحديث الإعدادات"""
    try:
        new_settings = request.get_json()
        web_manager.settings.update(new_settings)
        
        # حفظ الإعدادات
        os.makedirs('config', exist_ok=True)
        with open('config/bot_config.json', 'w', encoding='utf-8') as f:
            json.dump(web_manager.settings, f, ensure_ascii=False, indent=2)
        
        return jsonify({'success': True, 'message': 'تم تحديث الإعدادات بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/content')
def get_content():
    """الحصول على المحتوى المنشور"""
    try:
        db_stats = web_manager.get_database_stats()
        return jsonify({
            'success': True,
            'content': {
                'recent_articles': [],  # يمكن إضافة المقالات الحديثة هنا
                'total_count': db_stats['total_articles'],
                'today_count': db_stats['articles_today']
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/logs')
def get_logs():
    """الحصول على السجلات"""
    try:
        lines = request.args.get('lines', 50, type=int)
        log_file = 'logs/bot.log'
        
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
            return jsonify({
                'success': True,
                'logs': ''.join(recent_lines)
            })
        else:
            return jsonify({
                'success': True,
                'logs': 'لا توجد سجلات متاحة'
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/bot/start', methods=['POST'])
def start_bot():
    """بدء تشغيل البوت"""
    try:
        web_manager.bot_status = "starting"
        # هنا يمكن إضافة كود بدء البوت
        web_manager.bot_status = "running"
        
        return jsonify({'success': True, 'message': 'تم بدء تشغيل البوت'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/bot/stop', methods=['POST'])
def stop_bot():
    """إيقاف البوت"""
    try:
        web_manager.bot_status = "stopping"
        # هنا يمكن إضافة كود إيقاف البوت
        web_manager.bot_status = "offline"
        
        return jsonify({'success': True, 'message': 'تم إيقاف البوت'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/health')
def health_check():
    """فحص صحة الخادم (مهم للاستضافة)"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'Gaming News Agent Web API'
    })

def load_settings():
    """تحميل الإعدادات (للتوافق مع الكود الموجود)"""
    web_manager.load_settings()

def run_server(host='0.0.0.0', port=5000, debug=False):
    """تشغيل الخادم"""
    try:
        logger.info(f"🌐 بدء تشغيل خادم الواجهة الويب على http://{host}:{port}")
        app.run(host=host, port=port, debug=debug, threaded=True, use_reloader=False)
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل خادم الواجهة: {e}")

if __name__ == "__main__":
    # إنشاء المجلدات المطلوبة
    os.makedirs('data', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # تشغيل الخادم
    run_server(debug=True)
