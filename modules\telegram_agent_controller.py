#!/usr/bin/env python3
"""
تحكم في الوكيل عبر تيليجرام
"""

import asyncio
import subprocess
import psutil
import os
import signal
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading
import time
from modules.agent_data_manager import agent_data_manager

class TelegramAgentController:
    """تحكم في الوكيل عبر تيليجرام"""
    
    def __init__(self):
        self.agent_process = None
        self.agent_status = "stopped"
        self.last_start_time = None
        self.stats = {
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0,
            "last_error": None
        }
        
        # مسارات الملفات
        self.main_script = "main.py"
        self.log_file = "logs/bot.log"
        self.status_file = "data/bot_state.json"
        
        # تحميل الحالة المحفوظة
        self.load_state()
    
    def load_state(self):
        """تحميل حالة الوكيل المحفوظة"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.stats.update(data.get('stats', {}))
                    self.agent_status = data.get('status', 'stopped')
        except Exception as e:
            print(f"خطأ في تحميل حالة الوكيل: {e}")
    
    def save_state(self):
        """حفظ حالة الوكيل"""
        try:
            os.makedirs(os.path.dirname(self.status_file), exist_ok=True)
            data = {
                'status': self.agent_status,
                'stats': self.stats,
                'last_update': datetime.now().isoformat()
            }
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ حالة الوكيل: {e}")
    
    async def start_agent(self) -> Dict[str, Any]:
        """تشغيل الوكيل"""
        try:
            if self.agent_status == "running":
                return {
                    "success": False,
                    "message": "الوكيل يعمل بالفعل",
                    "status": self.agent_status
                }
            
            # التحقق من وجود الملف الرئيسي
            if not os.path.exists(self.main_script):
                return {
                    "success": False,
                    "message": f"ملف {self.main_script} غير موجود",
                    "status": "error"
                }
            
            # إنشاء مجلد السجلات
            os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
            
            # تشغيل الوكيل في عملية منفصلة
            self.agent_process = subprocess.Popen(
                ["python", self.main_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # انتظار قصير للتأكد من بدء التشغيل
            await asyncio.sleep(2)
            
            # التحقق من حالة العملية
            if self.agent_process.poll() is None:
                self.agent_status = "running"
                self.last_start_time = datetime.now()
                self.stats["total_runs"] += 1
                self.save_state()
                
                return {
                    "success": True,
                    "message": "تم تشغيل الوكيل بنجاح",
                    "status": self.agent_status,
                    "pid": self.agent_process.pid,
                    "start_time": self.last_start_time.isoformat()
                }
            else:
                # العملية توقفت فوراً
                stdout, stderr = self.agent_process.communicate()
                self.agent_status = "failed"
                self.stats["failed_runs"] += 1
                self.stats["last_error"] = stderr[:500] if stderr else "خطأ غير معروف"
                self.save_state()
                
                return {
                    "success": False,
                    "message": f"فشل في تشغيل الوكيل: {stderr[:200] if stderr else 'خطأ غير معروف'}",
                    "status": self.agent_status
                }
                
        except Exception as e:
            self.agent_status = "error"
            self.stats["failed_runs"] += 1
            self.stats["last_error"] = str(e)
            self.save_state()
            
            return {
                "success": False,
                "message": f"خطأ في تشغيل الوكيل: {str(e)}",
                "status": self.agent_status
            }
    
    async def stop_agent(self) -> Dict[str, Any]:
        """إيقاف الوكيل"""
        try:
            if self.agent_status != "running":
                return {
                    "success": False,
                    "message": "الوكيل غير مشغل حالياً",
                    "status": self.agent_status
                }
            
            if self.agent_process:
                # محاولة إيقاف العملية بلطف
                self.agent_process.terminate()
                
                # انتظار لمدة 5 ثوان
                try:
                    self.agent_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # إجبار الإيقاف إذا لم تتوقف
                    self.agent_process.kill()
                    self.agent_process.wait()
                
                self.agent_process = None
            
            self.agent_status = "stopped"
            self.stats["successful_runs"] += 1
            self.save_state()
            
            return {
                "success": True,
                "message": "تم إيقاف الوكيل بنجاح",
                "status": self.agent_status
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"خطأ في إيقاف الوكيل: {str(e)}",
                "status": "error"
            }
    
    async def restart_agent(self) -> Dict[str, Any]:
        """إعادة تشغيل الوكيل"""
        try:
            # إيقاف الوكيل أولاً
            stop_result = await self.stop_agent()
            
            # انتظار قصير
            await asyncio.sleep(3)
            
            # تشغيل الوكيل مرة أخرى
            start_result = await self.start_agent()
            
            return {
                "success": start_result["success"],
                "message": f"إعادة التشغيل: {start_result['message']}",
                "status": start_result["status"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"خطأ في إعادة تشغيل الوكيل: {str(e)}",
                "status": "error"
            }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """الحصول على حالة الوكيل"""
        try:
            # التحقق من حالة العملية الفعلية
            if self.agent_process:
                if self.agent_process.poll() is None:
                    # العملية لا تزال تعمل
                    self.agent_status = "running"
                else:
                    # العملية توقفت
                    self.agent_status = "stopped"
                    self.agent_process = None
            
            # معلومات إضافية
            uptime = None
            if self.agent_status == "running" and self.last_start_time:
                uptime = (datetime.now() - self.last_start_time).total_seconds()
            
            # معلومات النظام
            system_info = {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent
            }
            
            return {
                "status": self.agent_status,
                "uptime_seconds": uptime,
                "last_start_time": self.last_start_time.isoformat() if self.last_start_time else None,
                "stats": self.stats,
                "system_info": system_info,
                "process_id": self.agent_process.pid if self.agent_process else None
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "stats": self.stats
            }
    
    def get_recent_logs(self, lines: int = 20) -> List[str]:
        """الحصول على آخر سجلات الوكيل"""
        try:
            if not os.path.exists(self.log_file):
                return ["لا توجد سجلات متاحة"]
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return [line.strip() for line in all_lines[-lines:]]
                
        except Exception as e:
            return [f"خطأ في قراءة السجلات: {str(e)}"]
    
    def clear_logs(self) -> bool:
        """مسح سجلات الوكيل"""
        try:
            if os.path.exists(self.log_file):
                with open(self.log_file, 'w', encoding='utf-8') as f:
                    f.write(f"تم مسح السجلات في {datetime.now().isoformat()}\n")
                return True
            return False
        except Exception as e:
            print(f"خطأ في مسح السجلات: {e}")
            return False
    
    def get_system_health(self) -> Dict[str, Any]:
        """فحص صحة النظام"""
        try:
            health = {
                "overall": "healthy",
                "issues": [],
                "recommendations": []
            }
            
            # فحص استخدام الذاكرة
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                health["issues"].append("استخدام الذاكرة مرتفع جداً")
                health["recommendations"].append("إعادة تشغيل النظام أو إغلاق التطبيقات غير الضرورية")
                health["overall"] = "warning"
            
            # فحص استخدام القرص
            disk = psutil.disk_usage('/')
            if disk.percent > 95:
                health["issues"].append("مساحة القرص ممتلئة تقريباً")
                health["recommendations"].append("تنظيف الملفات المؤقتة أو حذف الملفات غير الضرورية")
                health["overall"] = "critical"
            
            # فحص وجود الملفات المهمة
            important_files = [self.main_script, "config/settings.py", ".env"]
            for file in important_files:
                if not os.path.exists(file):
                    health["issues"].append(f"ملف مهم مفقود: {file}")
                    health["overall"] = "critical"
            
            # فحص قاعدة البيانات
            db_files = ["data/articles.db", "data/api_keys.db"]
            for db_file in db_files:
                if not os.path.exists(db_file):
                    health["issues"].append(f"قاعدة بيانات مفقودة: {db_file}")
                    health["recommendations"].append("تشغيل الوكيل لإنشاء قواعد البيانات")
            
            return health
            
        except Exception as e:
            return {
                "overall": "error",
                "issues": [f"خطأ في فحص النظام: {str(e)}"],
                "recommendations": ["تحقق من سجلات النظام"]
            }

    async def reset_agent_with_cleanup(self, create_backup: bool = True) -> Dict[str, Any]:
        """إعادة تعيين الوكيل مع تنظيف البيانات"""
        try:
            # إيقاف الوكيل أولاً إذا كان يعمل
            if self.agent_status == "running":
                stop_result = await self.stop_agent()
                if not stop_result["success"]:
                    return {
                        "success": False,
                        "message": "فشل في إيقاف الوكيل قبل التنظيف",
                        "details": stop_result
                    }

            # إعادة تعيين بيانات الوكيل
            reset_info = agent_data_manager.reset_agent_data(create_backup)

            if reset_info["success"]:
                # تحديث الإحصائيات
                self.stats = {
                    "total_runs": 0,
                    "successful_runs": 0,
                    "failed_runs": 0,
                    "last_error": None
                }

                # حفظ الحالة الجديدة
                self.save_state()

                return {
                    "success": True,
                    "message": "تم إعادة تعيين الوكيل وتنظيف البيانات بنجاح",
                    "reset_info": reset_info,
                    "backup_created": reset_info.get("backup_created", False),
                    "freed_space": reset_info.get("cleanup_info", {}).get("total_freed_space", 0)
                }
            else:
                return {
                    "success": False,
                    "message": "فشل في إعادة تعيين بيانات الوكيل",
                    "error": reset_info.get("error", "خطأ غير معروف")
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"خطأ في إعادة تعيين الوكيل: {str(e)}",
                "error": str(e)
            }

    def get_data_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص البيانات"""
        try:
            return agent_data_manager.get_data_summary()
        except Exception as e:
            return {
                "error": f"خطأ في الحصول على ملخص البيانات: {str(e)}"
            }

    def create_data_backup(self) -> Dict[str, Any]:
        """إنشاء نسخة احتياطية من البيانات"""
        try:
            return agent_data_manager.create_backup()
        except Exception as e:
            return {
                "success": False,
                "error": f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
            }

    def get_backup_list(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            return agent_data_manager.get_backup_list()
        except Exception as e:
            return []

# إنشاء مثيل عام
telegram_agent_controller = TelegramAgentController()
