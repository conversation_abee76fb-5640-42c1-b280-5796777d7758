#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للوكيل في الوضع الخفيف
Quick Start for Gaming News Agent in Lite Mode
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # فحص الملفات المطلوبة
    required_files = [
        'main_lite.py',
        'requirements.txt',
        'config/lite_mode_config.py',
        'deployment_config.py'
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ ملف مفقود: {file_path}")
            return False
        print(f"✅ {file_path}")
    
    return True

def setup_environment():
    """إعداد متغيرات البيئة للوضع الخفيف"""
    print("⚙️ إعداد البيئة للوضع الخفيف...")
    
    # متغيرات الوضع الخفيف
    lite_env = {
        'LITE_MODE': 'true',
        'MAX_MEMORY_MB': '400',
        'PYTHONUNBUFFERED': '1',
        'PYTHONPATH': os.getcwd(),
        'TZ': 'UTC'
    }
    
    # تطبيق المتغيرات
    for key, value in lite_env.items():
        os.environ[key] = value
        print(f"  📝 {key}={value}")
    
    print("✅ تم إعداد البيئة")

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        # تحديث pip
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, capture_output=True)
        
        # تثبيت المتطلبات
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True)
        
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def check_memory():
    """فحص الذاكرة المتاحة"""
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_mb = memory.available / 1024 / 1024
        
        print(f"💾 الذاكرة المتاحة: {available_mb:.0f}MB")
        
        if available_mb < 512:
            print("⚠️ تحذير: الذاكرة المتاحة أقل من 512MB")
            return False
        
        print("✅ الذاكرة كافية")
        return True
        
    except ImportError:
        print("⚠️ لا يمكن فحص الذاكرة (psutil غير مثبت)")
        return True

def test_apis():
    """اختبار APIs الأساسية"""
    print("🔑 اختبار APIs...")
    
    # فحص متغيرات البيئة للـ APIs
    required_apis = [
        'GEMINI_API_KEY',
        'TELEGRAM_BOT_TOKEN'
    ]
    
    missing_apis = []
    for api in required_apis:
        if not os.getenv(api):
            missing_apis.append(api)
        else:
            print(f"✅ {api} موجود")
    
    if missing_apis:
        print("⚠️ APIs مفقودة:")
        for api in missing_apis:
            print(f"  - {api}")
        print("💡 يمكن إضافتها لاحقاً من خلال بوت Telegram")
    
    return len(missing_apis) == 0

def start_agent():
    """بدء تشغيل الوكيل"""
    print("🚀 بدء تشغيل الوكيل...")
    print("=" * 50)
    
    try:
        # تشغيل الوكيل
        subprocess.run([sys.executable, 'main_lite.py'], check=True)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الوكيل بواسطة المستخدم")
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تشغيل الوكيل: {e}")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

def show_status():
    """عرض حالة النظام"""
    print("📊 حالة النظام:")
    print(f"  🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  🐍 Python: {sys.version.split()[0]}")
    print(f"  📁 المجلد: {os.getcwd()}")
    print(f"  🔧 الوضع: خفيف (Lite Mode)")
    
    # فحص الذاكرة إذا كان psutil متاح
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"  💾 استهلاك الذاكرة: {memory_mb:.1f}MB")
    except ImportError:
        pass

def main():
    """الدالة الرئيسية"""
    print("🎮 وكيل أخبار الألعاب - الوضع الخفيف")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات")
        return
    
    # إعداد البيئة
    setup_environment()
    
    # فحص الذاكرة
    check_memory()
    
    # اختبار APIs
    test_apis()
    
    # عرض الحالة
    show_status()
    
    print("\n" + "=" * 50)
    print("🎯 خيارات التشغيل:")
    print("1. تثبيت المتطلبات")
    print("2. بدء الوكيل")
    print("3. عرض الحالة")
    print("4. خروج")
    
    while True:
        try:
            choice = input("\n👉 اختر رقم (1-4): ").strip()
            
            if choice == '1':
                install_requirements()
            elif choice == '2':
                start_agent()
                break
            elif choice == '3':
                show_status()
            elif choice == '4':
                print("👋 وداعاً!")
                break
            else:
                print("❌ خيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n👋 تم الإنهاء")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
