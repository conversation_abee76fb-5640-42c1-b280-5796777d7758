#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات الوضع الخفيف للاستضافة المجانية
Lite Mode Configuration for Free Hosting
"""

import os
from typing import Dict, List, Any

class LiteModeConfig:
    """إعدادات الوضع الخفيف المحسن للاستضافة المجانية"""
    
    # ===== إعدادات الذاكرة =====
    MAX_MEMORY_MB = int(os.getenv('MAX_MEMORY_MB', '400'))  # 400MB من 512MB
    MEMORY_WARNING_THRESHOLD = MAX_MEMORY_MB * 0.8  # تحذير عند 80%
    MEMORY_CLEANUP_THRESHOLD = MAX_MEMORY_MB * 0.9  # تنظيف عند 90%
    CLEANUP_INTERVAL_SECONDS = 300  # تنظيف كل 5 دقائق
    
    # ===== إعدادات المعالجة =====
    MAX_CONCURRENT_TASKS = 1  # مهمة واحدة في نفس الوقت
    TASK_TIMEOUT_SECONDS = 300  # مهلة 5 دقائق لكل مهمة
    RETRY_ATTEMPTS = 2  # محاولتان فقط
    RETRY_DELAY_SECONDS = 30  # انتظار 30 ثانية بين المحاولات
    
    # ===== إعدادات المحتوى =====
    MAX_ARTICLES_PER_HOUR = 2  # مقالان كحد أقصى في الساعة
    MAX_CONTENT_LENGTH = 2000  # حد أقصى 2000 حرف للمقال
    ENABLE_CONTENT_CACHING = False  # تعطيل التخزين المؤقت
    
    # ===== إعدادات APIs =====
    API_TIMEOUT_SECONDS = 30  # مهلة 30 ثانية للـ APIs
    MAX_API_RETRIES = 2  # محاولتان للـ API
    API_RATE_LIMIT_DELAY = 2  # انتظار ثانيتان بين طلبات API
    
    # ===== الوحدات المعطلة في الوضع الخفيف =====
    DISABLED_MODULES = [
        'advanced_rag_system',
        'multimodal_analyzer', 
        'semantic_search_engine',
        'memory_system',
        'advanced_youtube_analyzer',
        'local_ai_integration',
        'advanced_monitoring',
        'performance_optimizer'
    ]
    
    # ===== الوحدات الأساسية المفعلة =====
    ENABLED_MODULES = [
        'content_scraper',
        'content_generator', 
        'publisher',
        'web_approval_system',
        'simple_backup',
        'error_handler',
        'logger',
        'database'
    ]
    
    # ===== إعدادات قاعدة البيانات =====
    DB_CONFIG = {
        'connection_pool_size': 1,  # اتصال واحد فقط
        'max_overflow': 0,  # عدم السماح بتجاوز الاتصالات
        'pool_timeout': 30,
        'pool_recycle': 3600,  # إعادة تدوير الاتصالات كل ساعة
        'echo': False  # تعطيل تسجيل SQL
    }
    
    # ===== إعدادات التسجيل =====
    LOGGING_CONFIG = {
        'level': 'INFO',
        'max_file_size_mb': 10,  # حد أقصى 10MB للملف
        'backup_count': 2,  # ملفان احتياطيان فقط
        'console_only': True,  # تسجيل في الكونسول فقط
        'disable_debug': True
    }
    
    # ===== إعدادات الشبكة =====
    NETWORK_CONFIG = {
        'connection_timeout': 10,
        'read_timeout': 30,
        'max_connections': 5,
        'max_connections_per_host': 2,
        'enable_compression': True,
        'user_agent': 'GamingNewsAgent-Lite/1.0'
    }
    
    # ===== إعدادات التخزين المؤقت =====
    CACHE_CONFIG = {
        'enabled': False,  # تعطيل التخزين المؤقت
        'max_size_mb': 50,  # حد أقصى 50MB إذا تم التفعيل
        'ttl_seconds': 1800,  # 30 دقيقة
        'cleanup_interval': 600  # تنظيف كل 10 دقائق
    }
    
    # ===== إعدادات الصور =====
    IMAGE_CONFIG = {
        'max_size_mb': 5,  # حد أقصى 5MB للصورة
        'max_width': 1200,
        'max_height': 800,
        'quality': 80,  # جودة 80%
        'format': 'JPEG',  # JPEG فقط لتوفير المساحة
        'enable_local_processing': False  # استخدام APIs خارجية فقط
    }
    
    # ===== إعدادات الأمان =====
    SECURITY_CONFIG = {
        'rate_limit_enabled': True,
        'max_requests_per_minute': 30,
        'max_requests_per_hour': 100,
        'enable_cors': True,
        'allowed_origins': ['*'],  # يمكن تخصيصها
        'max_content_length_mb': 16
    }
    
    @classmethod
    def get_memory_config(cls) -> Dict[str, Any]:
        """الحصول على إعدادات الذاكرة"""
        return {
            'max_memory_mb': cls.MAX_MEMORY_MB,
            'warning_threshold': cls.MEMORY_WARNING_THRESHOLD,
            'cleanup_threshold': cls.MEMORY_CLEANUP_THRESHOLD,
            'cleanup_interval': cls.CLEANUP_INTERVAL_SECONDS
        }
    
    @classmethod
    def get_processing_config(cls) -> Dict[str, Any]:
        """الحصول على إعدادات المعالجة"""
        return {
            'max_concurrent_tasks': cls.MAX_CONCURRENT_TASKS,
            'task_timeout': cls.TASK_TIMEOUT_SECONDS,
            'retry_attempts': cls.RETRY_ATTEMPTS,
            'retry_delay': cls.RETRY_DELAY_SECONDS
        }
    
    @classmethod
    def get_content_config(cls) -> Dict[str, Any]:
        """الحصول على إعدادات المحتوى"""
        return {
            'max_articles_per_hour': cls.MAX_ARTICLES_PER_HOUR,
            'max_content_length': cls.MAX_CONTENT_LENGTH,
            'enable_caching': cls.ENABLE_CONTENT_CACHING
        }
    
    @classmethod
    def get_api_config(cls) -> Dict[str, Any]:
        """الحصول على إعدادات APIs"""
        return {
            'timeout': cls.API_TIMEOUT_SECONDS,
            'max_retries': cls.MAX_API_RETRIES,
            'rate_limit_delay': cls.API_RATE_LIMIT_DELAY
        }
    
    @classmethod
    def is_module_enabled(cls, module_name: str) -> bool:
        """فحص إذا كانت الوحدة مفعلة في الوضع الخفيف"""
        return module_name in cls.ENABLED_MODULES and module_name not in cls.DISABLED_MODULES
    
    @classmethod
    def get_all_config(cls) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات"""
        return {
            'memory': cls.get_memory_config(),
            'processing': cls.get_processing_config(),
            'content': cls.get_content_config(),
            'api': cls.get_api_config(),
            'database': cls.DB_CONFIG,
            'logging': cls.LOGGING_CONFIG,
            'network': cls.NETWORK_CONFIG,
            'cache': cls.CACHE_CONFIG,
            'image': cls.IMAGE_CONFIG,
            'security': cls.SECURITY_CONFIG,
            'enabled_modules': cls.ENABLED_MODULES,
            'disabled_modules': cls.DISABLED_MODULES
        }
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """التحقق من صحة الإعدادات"""
        warnings = []
        
        if cls.MAX_MEMORY_MB > 500:
            warnings.append("تحذير: الحد الأقصى للذاكرة أكبر من 500MB")
        
        if cls.MAX_CONCURRENT_TASKS > 2:
            warnings.append("تحذير: عدد المهام المتزامنة أكبر من 2")
        
        if cls.ENABLE_CONTENT_CACHING:
            warnings.append("تحذير: التخزين المؤقت مفعل - قد يستهلك ذاكرة إضافية")
        
        return warnings

# إنشاء مثيل الإعدادات
lite_config = LiteModeConfig()

def get_lite_config() -> LiteModeConfig:
    """الحصول على إعدادات الوضع الخفيف"""
    return lite_config

def is_lite_mode_enabled() -> bool:
    """فحص إذا كان الوضع الخفيف مفعل"""
    return os.getenv('LITE_MODE', 'false').lower() == 'true'

if __name__ == "__main__":
    # طباعة الإعدادات للاختبار
    config = lite_config.get_all_config()
    print("🔧 إعدادات الوضع الخفيف:")
    for section, settings in config.items():
        print(f"\n📋 {section}:")
        for key, value in settings.items():
            print(f"  - {key}: {value}")
    
    # التحقق من صحة الإعدادات
    warnings = lite_config.validate_config()
    if warnings:
        print("\n⚠️ تحذيرات:")
        for warning in warnings:
            print(f"  - {warning}")
    else:
        print("\n✅ جميع الإعدادات صحيحة")
