#!/usr/bin/env python3
"""
نظام إدارة بيانات الوكيل - تصنيف وتنظيف البيانات
"""

import os
import shutil
import sqlite3
import json
import glob
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import threading

@dataclass
class DataCategory:
    """فئة البيانات"""
    name: str
    description: str
    preserve: bool  # True = احتفظ، False = احذف
    paths: List[str]
    backup_required: bool = False

class AgentDataManager:
    """مدير بيانات الوكيل"""
    
    def __init__(self):
        self.lock = threading.RLock()
        
        # تصنيف البيانات
        self.data_categories = {
            # البيانات المحفوظة (لا تحذف أبداً)
            "api_keys": DataCategory(
                name="مفاتيح API",
                description="مفاتيح الخدمات والAPI",
                preserve=True,
                paths=["data/api_keys.db"],
                backup_required=True
            ),
            
            "auth_tokens": DataCategory(
                name="توكنات المصادقة",
                description="توكنات OAuth وحسابات الخدمة",
                preserve=True,
                paths=[
                    "config/blogger_token.json",
                    "config/bot_config.json",
                    "client_secret.json",
                    "service_account.json"
                ],
                backup_required=True
            ),
            
            "system_config": DataCategory(
                name="إعدادات النظام",
                description="ملفات التكوين الأساسية",
                preserve=True,
                paths=[
                    "config/settings.py",
                    ".env",
                    "config/api_config.py",
                    "config/sources_blacklist.py"
                ],
                backup_required=True
            ),
            
            # البيانات القابلة للحذف (بيانات الموقع)
            "articles_data": DataCategory(
                name="بيانات المقالات",
                description="قاعدة بيانات المقالات المنشورة",
                preserve=False,
                paths=["data/articles.db"],
                backup_required=True
            ),
            
            "cache_data": DataCategory(
                name="ملفات التخزين المؤقت",
                description="ملفات الكاش والبيانات المؤقتة",
                preserve=False,
                paths=[
                    "cache/",
                    "temp/",
                    "*.tmp",
                    "*.cache"
                ]
            ),
            
            "logs_data": DataCategory(
                name="ملفات السجلات",
                description="سجلات النظام والأخطاء",
                preserve=False,
                paths=[
                    "logs/",
                    "*.log"
                ]
            ),
            
            "images_data": DataCategory(
                name="الصور المؤقتة",
                description="صور المقالات والملفات المؤقتة",
                preserve=False,
                paths=[
                    "images/",
                    "assets/backgrounds/",
                    "*.png",
                    "*.jpg",
                    "*.jpeg"
                ]
            ),
            
            "state_data": DataCategory(
                name="حالة النظام",
                description="ملفات حالة البوت والنظام",
                preserve=False,
                paths=[
                    "data/bot_state.json",
                    "data/agent_state.json"
                ]
            ),
            
            "temp_files": DataCategory(
                name="الملفات المؤقتة",
                description="ملفات مؤقتة متنوعة",
                preserve=False,
                paths=[
                    "__pycache__/",
                    "*.pyc",
                    "*.pyo",
                    ".pytest_cache/",
                    "txtify_for_upload/",
                    "youtube_dl/"
                ]
            )
        }
    
    def get_data_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص البيانات"""
        summary = {
            "preserved_data": [],
            "deletable_data": [],
            "total_size": 0,
            "preserved_size": 0,
            "deletable_size": 0
        }
        
        for category_id, category in self.data_categories.items():
            category_info = {
                "id": category_id,
                "name": category.name,
                "description": category.description,
                "files": [],
                "total_size": 0
            }
            
            # فحص الملفات الموجودة
            for path_pattern in category.paths:
                files = self._find_files(path_pattern)
                for file_path in files:
                    if os.path.exists(file_path):
                        size = self._get_size(file_path)
                        category_info["files"].append({
                            "path": file_path,
                            "size": size,
                            "exists": True
                        })
                        category_info["total_size"] += size
            
            summary["total_size"] += category_info["total_size"]
            
            if category.preserve:
                summary["preserved_data"].append(category_info)
                summary["preserved_size"] += category_info["total_size"]
            else:
                summary["deletable_data"].append(category_info)
                summary["deletable_size"] += category_info["total_size"]
        
        return summary
    
    def create_backup(self, backup_dir: str = None) -> Dict[str, Any]:
        """إنشاء نسخة احتياطية من البيانات المهمة"""
        if not backup_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = f"backups/backup_{timestamp}"
        
        os.makedirs(backup_dir, exist_ok=True)
        
        backup_info = {
            "timestamp": datetime.now().isoformat(),
            "backup_dir": backup_dir,
            "backed_up_files": [],
            "errors": []
        }
        
        # نسخ البيانات المحفوظة والمهمة
        for category_id, category in self.data_categories.items():
            if category.preserve or category.backup_required:
                for path_pattern in category.paths:
                    files = self._find_files(path_pattern)
                    for file_path in files:
                        if os.path.exists(file_path):
                            try:
                                # إنشاء مسار النسخة الاحتياطية
                                backup_path = os.path.join(backup_dir, file_path)
                                os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                                
                                # نسخ الملف أو المجلد
                                if os.path.isdir(file_path):
                                    shutil.copytree(file_path, backup_path, dirs_exist_ok=True)
                                else:
                                    shutil.copy2(file_path, backup_path)
                                
                                backup_info["backed_up_files"].append({
                                    "original": file_path,
                                    "backup": backup_path,
                                    "category": category.name
                                })
                                
                            except Exception as e:
                                backup_info["errors"].append({
                                    "file": file_path,
                                    "error": str(e)
                                })
        
        # حفظ معلومات النسخة الاحتياطية
        backup_info_file = os.path.join(backup_dir, "backup_info.json")
        with open(backup_info_file, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)
        
        return backup_info
    
    def clean_deletable_data(self, categories: List[str] = None) -> Dict[str, Any]:
        """تنظيف البيانات القابلة للحذف"""
        if categories is None:
            # حذف جميع البيانات القابلة للحذف
            categories = [cat_id for cat_id, cat in self.data_categories.items() if not cat.preserve]
        
        cleanup_info = {
            "timestamp": datetime.now().isoformat(),
            "deleted_files": [],
            "errors": [],
            "total_freed_space": 0
        }
        
        for category_id in categories:
            if category_id not in self.data_categories:
                continue
                
            category = self.data_categories[category_id]
            if category.preserve:
                cleanup_info["errors"].append({
                    "category": category_id,
                    "error": "محاولة حذف بيانات محفوظة"
                })
                continue
            
            # حذف ملفات هذه الفئة
            for path_pattern in category.paths:
                files = self._find_files(path_pattern)
                for file_path in files:
                    if os.path.exists(file_path):
                        try:
                            size = self._get_size(file_path)
                            
                            if os.path.isdir(file_path):
                                shutil.rmtree(file_path)
                            else:
                                os.remove(file_path)
                            
                            cleanup_info["deleted_files"].append({
                                "path": file_path,
                                "category": category.name,
                                "size": size
                            })
                            cleanup_info["total_freed_space"] += size
                            
                        except Exception as e:
                            cleanup_info["errors"].append({
                                "file": file_path,
                                "error": str(e)
                            })
        
        return cleanup_info
    
    def reset_agent_data(self, create_backup: bool = True) -> Dict[str, Any]:
        """إعادة تعيين بيانات الوكيل (حذف بيانات الموقع مع الاحتفاظ بالإعدادات)"""
        reset_info = {
            "timestamp": datetime.now().isoformat(),
            "backup_created": False,
            "backup_info": None,
            "cleanup_info": None,
            "success": False
        }
        
        try:
            # إنشاء نسخة احتياطية أولاً
            if create_backup:
                backup_info = self.create_backup()
                reset_info["backup_created"] = True
                reset_info["backup_info"] = backup_info
            
            # تنظيف البيانات القابلة للحذف
            cleanup_info = self.clean_deletable_data()
            reset_info["cleanup_info"] = cleanup_info
            
            # إعادة إنشاء المجلدات الأساسية
            essential_dirs = ["data", "logs", "cache", "images"]
            for dir_name in essential_dirs:
                os.makedirs(dir_name, exist_ok=True)
            
            reset_info["success"] = True
            
        except Exception as e:
            reset_info["error"] = str(e)
        
        return reset_info
    
    def _find_files(self, path_pattern: str) -> List[str]:
        """البحث عن الملفات باستخدام نمط المسار"""
        if os.path.isabs(path_pattern):
            # مسار مطلق
            if os.path.exists(path_pattern):
                return [path_pattern]
            else:
                return []
        
        # استخدام glob للبحث عن الأنماط
        try:
            return glob.glob(path_pattern, recursive=True)
        except:
            # إذا فشل glob، تحقق من وجود المسار مباشرة
            if os.path.exists(path_pattern):
                return [path_pattern]
            return []
    
    def _get_size(self, path: str) -> int:
        """حساب حجم الملف أو المجلد"""
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            elif os.path.isdir(path):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(path):
                    for filename in filenames:
                        file_path = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(file_path)
                        except:
                            pass
                return total_size
            return 0
        except:
            return 0
    
    def get_backup_list(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة النسخ الاحتياطية"""
        backups = []
        backup_base_dir = "backups"
        
        if not os.path.exists(backup_base_dir):
            return backups
        
        for backup_dir in os.listdir(backup_base_dir):
            backup_path = os.path.join(backup_base_dir, backup_dir)
            if os.path.isdir(backup_path):
                info_file = os.path.join(backup_path, "backup_info.json")
                if os.path.exists(info_file):
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            backup_info = json.load(f)
                            backup_info["backup_dir"] = backup_dir
                            backup_info["size"] = self._get_size(backup_path)
                            backups.append(backup_info)
                    except:
                        pass
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        return backups

# إنشاء مثيل عام
agent_data_manager = AgentDataManager()
