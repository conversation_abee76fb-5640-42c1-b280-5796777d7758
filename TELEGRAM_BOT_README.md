# 🤖 بوت تيليجرام لإدارة وكيل أخبار الألعاب

## 📋 نظرة عامة

هذا بوت تيليجرام متقدم لإدارة والتحكم في وكيل أخبار الألعاب. يوفر واجهة تفاعلية سهلة الاستخدام لمراقبة النظام وإدارة مفاتيح API والتحكم في الوكيل.

## ✨ المميزات الرئيسية

### 🎮 للمستخدمين العاديين:
- 📊 مراقبة حالة النظام
- 📰 عرض آخر الأخبار المعالجة
- 📈 الإحصائيات العامة
- ℹ️ المساعدة والدعم

### 🔧 للمديرين:
- 🔑 **إدارة مفاتيح API:**
  - عرض جميع المفاتيح
  - إضافة مفاتيح جديدة
  - تحديث المفاتيح الموجودة
  - حذف المفاتيح
  - تصدير واستيراد المفاتيح
  - إحصائيات الاستخدام

- 🚀 **التحكم في الوكيل:**
  - تشغيل الوكيل
  - إيقاف الوكيل
  - إعادة تشغيل الوكيل
  - مراقبة الحالة المباشرة

- ⚙️ **إدارة النظام:**
  - تنظيف النظام
  - فحص الأخطاء
  - عرض السجلات
  - تقارير شاملة
  - إعادة تشغيل النظام

## 🚀 التثبيت والإعداد

### 1. إعداد بوت تيليجرام

1. تحدث مع [@BotFather](https://t.me/BotFather) في تيليجرام
2. أنشئ بوت جديد باستخدام `/newbot`
3. احصل على التوكن واحفظه

### 2. تحديث ملف .env

أضف التوكن إلى ملف `.env`:

```env
TELEGRAM_BOT_TOKEN=**********************************************
```

### 3. تحديد معرف المدير

في ملف `config/settings.py`، حدد معرف المدير:

```python
TELEGRAM_ADMIN_ID = "vandal324"  # اسم المستخدم أو المعرف الرقمي
```

### 4. تشغيل البوت

```bash
python run_telegram_bot.py
```

## 📱 كيفية الاستخدام

### البدء
1. ابحث عن البوت في تيليجرام
2. أرسل `/start`
3. ستظهر لك الواجهة الرئيسية مع الأزرار التفاعلية

### الواجهة الرئيسية
- **📊 حالة النظام**: عرض حالة الوكيل والمكونات
- **📰 آخر الأخبار**: عرض آخر المقالات المعالجة
- **📈 الإحصائيات**: إحصائيات الاستخدام والأداء
- **ℹ️ المساعدة**: دليل الاستخدام

### للمديرين فقط
- **🔑 إدارة API Keys**: إدارة شاملة لمفاتيح API
- **⚙️ إعدادات النظام**: تكوين وصيانة النظام
- **🚀 تشغيل الوكيل**: بدء تشغيل الوكيل
- **⏹️ إيقاف الوكيل**: إيقاف الوكيل

## 🔑 إدارة مفاتيح API

### عرض المفاتيح
- **📋 عرض جميع المفاتيح**: قائمة شاملة بجميع المفاتيح
- **🔍 البحث بالخدمة**: تصفية المفاتيح حسب نوع الخدمة

### إضافة مفتاح جديد
استخدم الأمر:
```
/add_key اسم_المفتاح نوع_الخدمة قيمة_المفتاح وصف_اختياري
```

مثال:
```
/add_key GEMINI_NEW Google_Gemini AIzaSyB... مفتاح جيميني جديد
```

### الخدمات المدعومة
- Google Gemini
- Telegram Bot
- OpenAI
- Claude
- YouTube Data
- Pexels Images
- Freepik
- FluxAI
- وغيرها...

## 📊 المراقبة والإحصائيات

### حالة النظام
- حالة الوكيل (يعمل/متوقف)
- وقت التشغيل
- معرف العملية
- استخدام الموارد (CPU, RAM, Disk)

### إحصائيات API Keys
- إجمالي المفاتيح
- المفاتيح النشطة
- عدد الاستخدامات
- أكثر الخدمات استخداماً

### تقارير النظام
- تقرير شامل عن حالة النظام
- إحصائيات الأداء
- سجل الأخطاء
- توصيات التحسين

## 🛠️ الصيانة والتنظيف

### تنظيف النظام
- مسح ملفات السجلات القديمة
- تحرير الذاكرة المؤقتة
- إزالة الملفات المؤقتة

### فحص الأخطاء
- عرض آخر السجلات
- تحليل المشاكل
- اقتراح الحلول

### إعادة التشغيل
- إعادة تشغيل الوكيل فقط
- إعادة تشغيل النظام بالكامل

## 🔒 الأمان

### صلاحيات المدير
- فقط المدير المحدد يمكنه الوصول لوظائف الإدارة
- المستخدمون العاديون يمكنهم المراقبة فقط

### حماية المفاتيح
- لا يتم عرض قيم المفاتيح كاملة
- التصدير لا يشمل قيم المفاتيح الفعلية
- تشفير البيانات الحساسة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

**البوت لا يرد:**
- تحقق من صحة التوكن
- تأكد من اتصال الإنترنت
- راجع سجلات النظام

**لا يمكن الوصول لوظائف الإدارة:**
- تحقق من معرف المدير في الإعدادات
- تأكد من استخدام المعرف الصحيح

**فشل في تشغيل الوكيل:**
- تحقق من وجود ملف main.py
- راجع مفاتيح API المطلوبة
- فحص سجلات الأخطاء

### الحصول على المساعدة
- استخدم زر "ℹ️ المساعدة" في البوت
- راجع سجلات النظام
- تحقق من تقرير النظام

## 📞 الدعم

للحصول على الدعم:
1. استخدم وظيفة "فحص الأخطاء" في البوت
2. راجع السجلات والتقارير
3. تواصل مع المطور مع تفاصيل المشكلة

---

**تم تطوير هذا البوت لتسهيل إدارة وكيل أخبار الألعاب وتوفير تحكم كامل عبر تيليجرام** 🎮🤖
