#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النسخة المحسنة للوكيل
Test Script for Lite Version of Gaming News Agent
"""

import os
import sys
import time
import asyncio
from datetime import datetime
import traceback

# إعداد البيئة للوضع الخفيف
os.environ['LITE_MODE'] = 'true'
os.environ['MAX_MEMORY_MB'] = '400'
os.environ['PYTHONUNBUFFERED'] = '1'

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات الأساسية"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        # اختبار الوحدات الأساسية
        from modules.logger import logger
        print("✅ logger")
        
        from modules.memory_manager_lite import memory_manager
        print("✅ memory_manager_lite")
        
        from modules.content_scraper_lite import get_content_scraper
        print("✅ content_scraper_lite")
        
        from config.lite_mode_config import lite_config
        print("✅ lite_mode_config")
        
        from deployment_config import setup_deployment
        print("✅ deployment_config")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def test_memory_manager():
    """اختبار مدير الذاكرة"""
    print("\n🧠 اختبار مدير الذاكرة...")
    
    try:
        from modules.memory_manager_lite import memory_manager
        
        # فحص الحالة الأولية
        initial_stats = memory_manager.get_stats()
        print(f"  📊 الذاكرة الحالية: {initial_stats['current_usage_mb']:.1f}MB")
        print(f"  📈 الحد الأقصى: {initial_stats['max_memory_mb']}MB")
        
        # اختبار تنظيف الذاكرة
        cleanup_result = memory_manager.cleanup_memory()
        print(f"  🧹 تم تحرير: {cleanup_result['freed_mb']:.1f}MB")
        print(f"  ⏱️ وقت التنظيف: {cleanup_result['duration_seconds']:.2f}s")
        
        # اختبار فحص الحالة
        status = memory_manager.check_memory_status()
        print(f"  📊 حالة الذاكرة: {status['status']}")
        print(f"  📈 نسبة الاستخدام: {status['usage_percent']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الذاكرة: {e}")
        traceback.print_exc()
        return False

def test_content_scraper():
    """اختبار مستخرج المحتوى"""
    print("\n📰 اختبار مستخرج المحتوى...")
    
    try:
        from modules.content_scraper_lite import get_content_scraper
        
        scraper = get_content_scraper()
        print("  ✅ تم إنشاء مستخرج المحتوى")
        
        # اختبار الإحصائيات
        stats = scraper.get_content_stats()
        print(f"  📊 حجم الكاش: {stats['cache_size']}")
        print(f"  ❌ مصادر معطلة: {stats['failed_sources']}")
        print(f"  📡 إجمالي المصادر: {stats['total_sources']}")
        
        # اختبار تنظيف الكاش
        scraper.clear_cache()
        print("  🧹 تم تنظيف الكاش")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مستخرج المحتوى: {e}")
        traceback.print_exc()
        return False

async def test_content_fetching():
    """اختبار جلب المحتوى"""
    print("\n🔍 اختبار جلب المحتوى...")
    
    try:
        from modules.content_scraper_lite import get_content_scraper
        
        scraper = get_content_scraper()
        
        # محاولة جلب محتوى
        print("  🌐 محاولة جلب المحتوى...")
        content = await scraper.get_latest_content()
        
        if content:
            print(f"  ✅ تم جلب المحتوى: {content['title'][:50]}...")
            print(f"  📡 المصدر: {content['source']}")
            print(f"  📝 الوصف: {content['description'][:100]}...")
            return True
        else:
            print("  ℹ️ لا يوجد محتوى جديد")
            return True  # هذا طبيعي
            
    except Exception as e:
        print(f"❌ خطأ في جلب المحتوى: {e}")
        traceback.print_exc()
        return False

def test_lite_config():
    """اختبار إعدادات الوضع الخفيف"""
    print("\n⚙️ اختبار إعدادات الوضع الخفيف...")
    
    try:
        from config.lite_mode_config import lite_config, is_lite_mode_enabled
        
        # فحص تفعيل الوضع الخفيف
        is_enabled = is_lite_mode_enabled()
        print(f"  🔧 الوضع الخفيف مفعل: {is_enabled}")
        
        # فحص الإعدادات
        memory_config = lite_config.get_memory_config()
        print(f"  💾 الحد الأقصى للذاكرة: {memory_config['max_memory_mb']}MB")
        
        processing_config = lite_config.get_processing_config()
        print(f"  ⚡ المهام المتزامنة: {processing_config['max_concurrent_tasks']}")
        
        # فحص الوحدات المفعلة
        enabled_modules = lite_config.ENABLED_MODULES
        print(f"  📦 الوحدات المفعلة: {len(enabled_modules)}")
        
        disabled_modules = lite_config.DISABLED_MODULES
        print(f"  ❌ الوحدات المعطلة: {len(disabled_modules)}")
        
        # التحقق من صحة الإعدادات
        warnings = lite_config.validate_config()
        if warnings:
            print("  ⚠️ تحذيرات:")
            for warning in warnings:
                print(f"    - {warning}")
        else:
            print("  ✅ جميع الإعدادات صحيحة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        traceback.print_exc()
        return False

def test_deployment_config():
    """اختبار إعدادات النشر"""
    print("\n🚀 اختبار إعدادات النشر...")
    
    try:
        from deployment_config import deployment_config, setup_deployment
        
        # إعداد النشر
        setup_result = setup_deployment()
        print(f"  ✅ إعداد النشر: {setup_result}")
        
        # فحص المنصة
        platform = deployment_config.platform
        print(f"  🌐 المنصة: {platform}")
        
        # فحص إعدادات الأداء
        perf_config = deployment_config.get_performance_config()
        print(f"  ⚡ العمال: {perf_config['max_workers']}")
        print(f"  ⏱️ المهلة: {perf_config['timeout']}s")
        
        if 'memory_limit_mb' in perf_config:
            print(f"  💾 حد الذاكرة: {perf_config['memory_limit_mb']}MB")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات النشر: {e}")
        traceback.print_exc()
        return False

def test_web_api():
    """اختبار واجهة الويب"""
    print("\n🌐 اختبار واجهة الويب...")
    
    try:
        from web_api import app
        
        # فحص إعدادات التطبيق
        print(f"  🔧 سرية التطبيق: {'✅' if app.config.get('SECRET_KEY') else '❌'}")
        print(f"  🌍 دعم العربية: {'✅' if not app.config.get('JSON_AS_ASCII') else '❌'}")
        
        # فحص الوضع الخفيف
        if os.getenv('LITE_MODE', 'false').lower() == 'true':
            print("  🔧 تحسينات الوضع الخفيف مفعلة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الويب: {e}")
        traceback.print_exc()
        return False

async def run_full_test():
    """تشغيل الاختبار الكامل"""
    print("🎮 اختبار النسخة المحسنة لوكيل أخبار الألعاب")
    print("=" * 60)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("مدير الذاكرة", test_memory_manager),
        ("مستخرج المحتوى", test_content_scraper),
        ("إعدادات الوضع الخفيف", test_lite_config),
        ("إعدادات النشر", test_deployment_config),
        ("واجهة الويب", test_web_api),
    ]
    
    async_tests = [
        ("جلب المحتوى", test_content_fetching),
    ]
    
    passed = 0
    total = len(tests) + len(async_tests)
    
    # تشغيل الاختبارات العادية
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    # تشغيل الاختبارات غير المتزامنة
    for test_name, test_func in async_tests:
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    print(f"📈 معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النسخة المحسنة جاهزة للنشر")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(run_full_test())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ فادح في الاختبار: {e}")
        traceback.print_exc()
        sys.exit(1)
