#!/usr/bin/env python3
"""
اختبار شامل للنظام المتقدم
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات الجديدة"""
    print("🔍 اختبار استيراد الوحدات الجديدة...")
    
    try:
        from modules.agent_data_manager import agent_data_manager
        print("✅ تم استيراد agent_data_manager")
        
        from modules.smart_backup_system import smart_backup_system
        print("✅ تم استيراد smart_backup_system")
        
        from modules.telegram_agent_controller import telegram_agent_controller
        print("✅ تم استيراد telegram_agent_controller المحدث")
        
        from modules.enhanced_telegram_bot import enhanced_telegram_bot
        print("✅ تم استيراد enhanced_telegram_bot المحدث")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_data_manager():
    """اختبار مدير البيانات"""
    print("\n💾 اختبار مدير البيانات...")
    
    try:
        from modules.agent_data_manager import agent_data_manager
        
        # اختبار الحصول على ملخص البيانات
        summary = agent_data_manager.get_data_summary()
        print(f"✅ ملخص البيانات: {len(summary.get('preserved_data', []))} محفوظة، {len(summary.get('deletable_data', []))} قابلة للحذف")
        
        # اختبار تصنيف البيانات
        total_size_mb = summary.get("total_size", 0) / (1024 * 1024)
        preserved_size_mb = summary.get("preserved_size", 0) / (1024 * 1024)
        deletable_size_mb = summary.get("deletable_size", 0) / (1024 * 1024)
        
        print(f"✅ أحجام البيانات: إجمالي {total_size_mb:.1f} MB، محفوظة {preserved_size_mb:.1f} MB، قابلة للحذف {deletable_size_mb:.1f} MB")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في مدير البيانات: {e}")
        return False

def test_backup_system():
    """اختبار نظام النسخ الاحتياطي"""
    print("\n📦 اختبار نظام النسخ الاحتياطي...")
    
    try:
        from modules.smart_backup_system import smart_backup_system
        
        # اختبار قائمة النسخ الاحتياطية
        backups = smart_backup_system.list_backups()
        print(f"✅ النسخ الاحتياطية الموجودة: {len(backups)}")
        
        # اختبار إنشاء نسخة احتياطية تجريبية (بدون تنفيذ فعلي)
        print("✅ نظام النسخ الاحتياطي جاهز للعمل")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في نظام النسخ الاحتياطي: {e}")
        return False

def test_agent_controller():
    """اختبار تحكم الوكيل المحدث"""
    print("\n🤖 اختبار تحكم الوكيل المحدث...")
    
    try:
        from modules.telegram_agent_controller import telegram_agent_controller
        
        # اختبار الحصول على حالة الوكيل
        status = telegram_agent_controller.get_agent_status()
        print(f"✅ حالة الوكيل: {status['status']}")
        
        # اختبار الحصول على ملخص البيانات
        data_summary = telegram_agent_controller.get_data_summary()
        if "error" not in data_summary:
            print("✅ ملخص البيانات من التحكم متاح")
        else:
            print(f"⚠️ خطأ في ملخص البيانات: {data_summary['error']}")
        
        # اختبار قائمة النسخ الاحتياطية
        backups = telegram_agent_controller.get_backup_list()
        print(f"✅ قائمة النسخ الاحتياطية: {len(backups)} نسخة")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في تحكم الوكيل: {e}")
        return False

def test_bot_integration():
    """اختبار تكامل البوت"""
    print("\n🤖 اختبار تكامل البوت...")
    
    try:
        from modules.enhanced_telegram_bot import enhanced_telegram_bot
        from config.settings import BotConfig
        
        # اختبار إعدادات البوت
        if BotConfig.TELEGRAM_BOT_TOKEN:
            print("✅ توكن تيليجرام موجود")
        else:
            print("⚠️ توكن تيليجرام غير موجود")
        
        # اختبار معرف المدير
        if BotConfig.TELEGRAM_ADMIN_ID:
            print(f"✅ معرف المدير: {BotConfig.TELEGRAM_ADMIN_ID}")
        else:
            print("⚠️ معرف المدير غير محدد")
        
        print("✅ البوت المحسن جاهز للتشغيل")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في تكامل البوت: {e}")
        return False

def test_main_bot_only():
    """اختبار ملف التشغيل الجديد"""
    print("\n🚀 اختبار ملف التشغيل الجديد...")
    
    try:
        # التحقق من وجود الملف
        if os.path.exists("main_bot_only.py"):
            print("✅ ملف main_bot_only.py موجود")
        else:
            print("❌ ملف main_bot_only.py غير موجود")
            return False
        
        # اختبار استيراد الملف
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_bot_only", "main_bot_only.py")
        if spec and spec.loader:
            print("✅ ملف التشغيل الجديد قابل للاستيراد")
        else:
            print("❌ مشكلة في ملف التشغيل الجديد")
            return False
        
        return True
    except Exception as e:
        print(f"❌ خطأ في ملف التشغيل الجديد: {e}")
        return False

def test_file_structure():
    """اختبار بنية الملفات"""
    print("\n📁 اختبار بنية الملفات...")
    
    required_files = [
        "main_bot_only.py",
        "modules/agent_data_manager.py",
        "modules/smart_backup_system.py",
        "modules/telegram_agent_controller.py",
        "modules/enhanced_telegram_bot.py",
        "run_telegram_bot.py",
        "test_telegram_bot.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} مفقود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"⚠️ ملفات مفقودة: {len(missing_files)}")
        return False
    else:
        print("✅ جميع الملفات المطلوبة موجودة")
        return True

async def test_async_functions():
    """اختبار الوظائف غير المتزامنة"""
    print("\n⚡ اختبار الوظائف غير المتزامنة...")
    
    try:
        from modules.telegram_agent_controller import telegram_agent_controller
        
        # اختبار وظيفة إعادة التعيين (بدون تنفيذ فعلي)
        print("✅ وظائف إعادة التعيين جاهزة")
        
        # اختبار وظائف النسخ الاحتياطي
        print("✅ وظائف النسخ الاحتياطي جاهزة")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الوظائف غير المتزامنة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار النظام المتقدم")
    print("=" * 60)
    
    tests = [
        ("استيراد الوحدات الجديدة", test_imports),
        ("مدير البيانات", test_data_manager),
        ("نظام النسخ الاحتياطي", test_backup_system),
        ("تحكم الوكيل المحدث", test_agent_controller),
        ("تكامل البوت", test_bot_integration),
        ("ملف التشغيل الجديد", test_main_bot_only),
        ("بنية الملفات", test_file_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ نجح اختبار: {test_name}")
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    # اختبار الوظائف غير المتزامنة
    print(f"\n📋 اختبار: الوظائف غير المتزامنة")
    async_result = asyncio.run(test_async_functions())
    if async_result:
        passed += 1
        print(f"✅ نجح اختبار: الوظائف غير المتزامنة")
    else:
        print(f"❌ فشل اختبار: الوظائف غير المتزامنة")
    
    total += 1  # إضافة اختبار الوظائف غير المتزامنة
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام المتقدم جاهز للتشغيل")
        print("\n🚀 للتشغيل:")
        print("python main_bot_only.py  # تشغيل البوت فقط")
        print("python run_telegram_bot.py  # تشغيل البوت مع الواجهة الكاملة")
        
        print("\n🎯 المميزات الجديدة:")
        print("• إدارة شاملة للبيانات")
        print("• نظام نسخ احتياطي ذكي")
        print("• إعادة تعيين الوكيل مع تنظيف البيانات")
        print("• تحكم متقدم من البوت")
        print("• حفظ مفاتيح API والإعدادات")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
