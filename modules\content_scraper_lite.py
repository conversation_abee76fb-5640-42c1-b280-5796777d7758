#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام استخراج المحتوى المحسن للوضع الخفيف
Lite Content Scraper for Free Hosting
"""

import requests
from bs4 import BeautifulSoup
import time
import re
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional
import json
import gc
from urllib.parse import urljoin, urlparse

try:
    from .logger import logger
    from .database import db
    from config.lite_mode_config import lite_config
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

class ContentScraperLite:
    """محرك استخراج المحتوى المحسن للوضع الخفيف"""
    
    def __init__(self):
        # إعدادات الشبكة المحسنة
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'GamingNewsAgent-Lite/1.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'close'  # عدم الاحتفاظ بالاتصالات
        })
        
        # إعدادات محسنة للذاكرة
        self.timeout = 15  # مهلة قصيرة
        self.max_retries = 1  # محاولة واحدة فقط
        self.min_delay = 3  # 3 ثوان بين الطلبات
        self.last_request_time = {}
        self.failed_sources = set()
        
        # مصادر الأخبار المحسنة (APIs فقط)
        self.gaming_sources = [
            {
                'name': 'GameSpot RSS',
                'url': 'https://www.gamespot.com/feeds/news/',
                'type': 'rss',
                'priority': 1
            },
            {
                'name': 'IGN RSS', 
                'url': 'https://feeds.ign.com/ign/news',
                'type': 'rss',
                'priority': 2
            },
            {
                'name': 'Polygon RSS',
                'url': 'https://www.polygon.com/rss/index.xml',
                'type': 'rss', 
                'priority': 3
            }
        ]
        
        # كاش محدود
        self.content_cache = {}
        self.cache_max_size = 10  # 10 عناصر فقط
        self.cache_ttl = 1800  # 30 دقيقة
        
        logger.info("✅ تم تهيئة مستخرج المحتوى الخفيف")
    
    def clear_cache(self):
        """تنظيف الكاش لتوفير الذاكرة"""
        self.content_cache.clear()
        gc.collect()
        logger.info("🧹 تم تنظيف كاش المحتوى")
    
    def _respect_rate_limit(self, source_name: str):
        """احترام حدود المعدل"""
        current_time = time.time()
        last_time = self.last_request_time.get(source_name, 0)
        
        if current_time - last_time < self.min_delay:
            sleep_time = self.min_delay - (current_time - last_time)
            time.sleep(sleep_time)
        
        self.last_request_time[source_name] = time.time()
    
    def _fetch_url(self, url: str, source_name: str) -> Optional[str]:
        """جلب محتوى URL مع تحسينات الذاكرة"""
        try:
            self._respect_rate_limit(source_name)
            
            response = self.session.get(
                url,
                timeout=self.timeout,
                stream=True  # تدفق البيانات لتوفير الذاكرة
            )
            
            if response.status_code == 200:
                # قراءة المحتوى بحجم محدود
                content = response.content[:1024*1024]  # حد أقصى 1MB
                response.close()  # إغلاق الاتصال فوراً
                return content.decode('utf-8', errors='ignore')
            
            response.close()
            return None
            
        except Exception as e:
            logger.warning(f"فشل في جلب {url}: {e}")
            return None
    
    def _parse_rss_feed(self, content: str, source_name: str) -> List[Dict]:
        """تحليل RSS feed مع تحسينات الذاكرة"""
        try:
            soup = BeautifulSoup(content, 'xml')
            items = []
            
            # معالجة أول 5 عناصر فقط لتوفير الذاكرة
            for item in soup.find_all('item')[:5]:
                try:
                    title = item.find('title')
                    link = item.find('link')
                    description = item.find('description')
                    pub_date = item.find('pubDate')
                    
                    if title and link:
                        article_data = {
                            'title': title.get_text(strip=True),
                            'url': link.get_text(strip=True),
                            'description': description.get_text(strip=True) if description else '',
                            'source': source_name,
                            'published_at': pub_date.get_text(strip=True) if pub_date else '',
                            'content_type': 'gaming_news'
                        }
                        
                        # فلترة المحتوى المتعلق بالألعاب
                        if self._is_gaming_content(article_data['title'], article_data['description']):
                            items.append(article_data)
                
                except Exception as e:
                    logger.warning(f"خطأ في تحليل عنصر RSS: {e}")
                    continue
            
            # تنظيف الذاكرة
            del soup
            gc.collect()
            
            return items
            
        except Exception as e:
            logger.error(f"خطأ في تحليل RSS: {e}")
            return []
    
    def _is_gaming_content(self, title: str, description: str) -> bool:
        """فحص إذا كان المحتوى متعلق بالألعاب"""
        gaming_keywords = [
            'game', 'gaming', 'gamer', 'gameplay', 'playstation', 'xbox', 
            'nintendo', 'pc gaming', 'mobile game', 'esports', 'steam',
            'لعبة', 'ألعاب', 'جيمر', 'بلايستيشن', 'إكسبوكس'
        ]
        
        text = f"{title} {description}".lower()
        return any(keyword in text for keyword in gaming_keywords)
    
    def _check_cache(self, cache_key: str) -> Optional[Dict]:
        """فحص الكاش"""
        if cache_key in self.content_cache:
            cached_item = self.content_cache[cache_key]
            if time.time() - cached_item['timestamp'] < self.cache_ttl:
                return cached_item['data']
            else:
                del self.content_cache[cache_key]
        return None
    
    def _store_cache(self, cache_key: str, data: Dict):
        """حفظ في الكاش مع إدارة الحجم"""
        if len(self.content_cache) >= self.cache_max_size:
            # إزالة أقدم عنصر
            oldest_key = min(self.content_cache.keys(), 
                           key=lambda k: self.content_cache[k]['timestamp'])
            del self.content_cache[oldest_key]
        
        self.content_cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
    
    async def get_latest_content(self) -> Optional[Dict]:
        """الحصول على أحدث محتوى (محسن للذاكرة)"""
        try:
            all_articles = []
            
            # معالجة المصادر واحد تلو الآخر لتوفير الذاكرة
            for source in self.gaming_sources:
                if source['name'] in self.failed_sources:
                    continue
                
                try:
                    logger.info(f"🔍 جلب المحتوى من {source['name']}")
                    
                    # فحص الكاش أولاً
                    cache_key = f"source_{source['name']}"
                    cached_content = self._check_cache(cache_key)
                    
                    if cached_content:
                        all_articles.extend(cached_content)
                        continue
                    
                    # جلب المحتوى
                    content = self._fetch_url(source['url'], source['name'])
                    if not content:
                        self.failed_sources.add(source['name'])
                        continue
                    
                    # تحليل المحتوى
                    if source['type'] == 'rss':
                        articles = self._parse_rss_feed(content, source['name'])
                    else:
                        articles = []
                    
                    if articles:
                        all_articles.extend(articles)
                        self._store_cache(cache_key, articles)
                        logger.info(f"✅ تم جلب {len(articles)} مقال من {source['name']}")
                    
                    # تنظيف الذاكرة بعد كل مصدر
                    del content
                    gc.collect()
                    
                    # توقف قصير بين المصادر
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"خطأ في معالجة {source['name']}: {e}")
                    self.failed_sources.add(source['name'])
                    continue
            
            # اختيار أفضل مقال
            if all_articles:
                # ترتيب حسب الأولوية والتاريخ
                all_articles.sort(key=lambda x: (
                    x.get('priority', 999),
                    x.get('published_at', '')
                ), reverse=True)
                
                best_article = all_articles[0]
                logger.info(f"📰 تم اختيار المقال: {best_article['title'][:50]}...")
                
                # تنظيف الذاكرة
                del all_articles
                gc.collect()
                
                return best_article
            
            logger.info("ℹ️ لا يوجد محتوى جديد")
            return None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على المحتوى: {e}")
            return None
    
    def get_content_stats(self) -> Dict:
        """إحصائيات المحتوى"""
        return {
            'cache_size': len(self.content_cache),
            'failed_sources': len(self.failed_sources),
            'total_sources': len(self.gaming_sources),
            'last_update': datetime.now().strftime('%H:%M:%S')
        }

# إنشاء مثيل للاستخدام
content_scraper_lite = ContentScraperLite()

def get_content_scraper():
    """الحصول على مستخرج المحتوى المناسب"""
    import os
    if os.getenv('LITE_MODE', 'false').lower() == 'true':
        return content_scraper_lite
    else:
        # استيراد النسخة العادية
        from .content_scraper import ContentScraper
        return ContentScraper()
