# متطلبات وكيل أخبار الألعاب - محسن للاستضافة المجانية (512MB RAM)
# تم تحسينه للعمل مع APIs خارجية وتقليل استهلاك الذاكرة

# ===== الذكاء الاصطناعي (APIs خارجية فقط) =====
google-generativeai>=0.8.0
google-api-python-client>=2.110.0
google-auth-oauthlib>=1.1.0
google-auth>=2.23.0

# ===== خادم الويب (خفيف) =====
flask>=2.3.0
flask-cors>=4.0.0

# ===== تيليجرام =====
python-telegram-bot==20.6
aiohttp>=3.9.0

# ===== استخراج المحتوى ومعالجة الويب =====
beautifulsoup4>=4.12.0
requests>=2.31.0
lxml>=4.9.0
html5lib>=1.1

# ===== معالجة النصوص الأساسية =====
python-dateutil>=2.8.0

# ===== YouTube (API فقط) =====
youtube-transcript-api>=1.6.0

# ===== الصور (خفيف - Pillow فقط) =====
Pillow>=10.1.0

# ===== الأدوات المساعدة =====
aiofiles>=23.2.0
python-dotenv>=1.0.0
schedule>=1.2.0
pytz>=2023.3

# ===== JSON والبيانات =====
ujson>=5.8.0

# ===== التسجيل =====
colorlog>=6.7.0

# ===== أدوات الشبكة =====
httpx>=0.25.0

# ===== معالجة الوقت والتاريخ =====
arrow>=1.3.0

# ===== أدوات التحكم =====
click>=8.1.0

# ===== أدوات النظام الأساسية =====
psutil>=5.9.0

# ===== ملاحظات التحسين =====
# تم إزالة المكتبات الثقيلة التالية:
# - nltk, textblob (استبدال بـ Gemini API)
# - pandas, numpy (استبدال بمعالجة بسيطة)
# - opencv-python (استبدال بـ Pillow + APIs خارجية)
# - matplotlib (استبدال بـ APIs خارجية للرسوم البيانية)
# - imageio (غير مطلوب مع Pillow)
# - pydantic (استبدال بـ dict عادي)

# ===== تقدير استهلاك الذاكرة =====
# النسخة الحالية: ~200-250MB RAM
# مقارنة بالنسخة السابقة: ~600MB RAM
# توفير: ~60% من الذاكرة
