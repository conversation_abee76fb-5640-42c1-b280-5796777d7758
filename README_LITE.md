# 🎮 وكيل أخبار الألعاب - النسخة المحسنة للاستضافة المجانية

## 🚀 نظرة عامة

هذه النسخة المحسنة من وكيل أخبار الألعاب مصممة خصيصاً للعمل على الاستضافة المجانية مع موارد محدودة:

- **512MB RAM** 
- **0.1 CPU**
- **APIs خارجية للذكاء الاصطناعي**
- **معالجة محلية خفيفة**

## ✨ المميزات المحتفظ بها

### 🤖 الذكاء الاصطناعي
- ✅ **Google Gemini** - للكتابة والتحليل (مجاني تماماً)
- ✅ **Hugging Face** - نماذج AI مختلفة (1000 طلب/شهر)
- ✅ **Whisper API** - تحويل الصوت إلى نص
- ✅ **APIs خارجية للصور** - إنشاء ومعالجة الصور

### 📰 استخراج المحتوى
- ✅ **RSS Feeds** - من مواقع الألعاب الرئيسية
- ✅ **Web Scraping** محسن وخفيف
- ✅ **YouTube Transcript** - استخراج النصوص من الفيديوهات
- ✅ **فلترة ذكية** للمحتوى المتعلق بالألعاب

### 🎨 إنشاء المحتوى
- ✅ **كتابة تلقائية** بالذكاء الاصطناعي
- ✅ **تحسين SEO** أساسي
- ✅ **إنشاء الصور** عبر APIs خارجية
- ✅ **تنسيق احترافي** للمقالات

### 📤 النشر والتوزيع
- ✅ **Blogger** - نشر تلقائي
- ✅ **Telegram Bot** - للإدارة والتحكم
- ✅ **واجهة ويب** خفيفة للمراقبة

## 🔧 التحسينات المطبقة

### 💾 تحسينات الذاكرة
- **مدير ذاكرة متقدم** مع مراقبة مستمرة
- **تنظيف دوري** للذاكرة كل 5 دقائق
- **Lazy Loading** للوحدات
- **كاش محدود** الحجم مع TTL
- **إزالة المكتبات الثقيلة** (PyTorch, OpenCV, إلخ)

### ⚡ تحسينات الأداء
- **معالجة متسلسلة** بدلاً من المتوازية
- **مهلة قصيرة** للطلبات (15 ثانية)
- **محاولة واحدة** فقط لكل طلب
- **إغلاق فوري** للاتصالات
- **تنظيف تلقائي** بعد كل عملية

### 🔌 APIs خارجية فقط
- **لا معالجة محلية ثقيلة** للصور أو الصوت
- **اعتماد كامل** على خدمات السحابة
- **تقليل استهلاك CPU** إلى الحد الأدنى

## 📦 التثبيت والتشغيل

### 1. التحضير السريع
```bash
# تشغيل سكريبت التحضير
python start_lite.py
```

### 2. التثبيت اليدوي
```bash
# تثبيت المتطلبات المحسنة
pip install -r requirements.txt

# إعداد متغيرات البيئة
export LITE_MODE=true
export MAX_MEMORY_MB=400

# تشغيل الوكيل
python main_lite.py
```

### 3. النشر على Render
```bash
# رفع الملفات إلى Git repository
git add .
git commit -m "النسخة المحسنة للاستضافة المجانية"
git push

# ربط مع Render وسيتم النشر تلقائياً
```

## 🧪 الاختبار

```bash
# اختبار شامل للنسخة المحسنة
python test_lite_version.py
```

## 📊 مقارنة الأداء

| المورد | النسخة العادية | النسخة المحسنة | التوفير |
|---------|----------------|-----------------|----------|
| **RAM** | ~600MB | ~200MB | **66%** |
| **CPU** | عالي | منخفض | **70%** |
| **التخزين** | ~2GB | ~500MB | **75%** |
| **المكتبات** | 50+ | 15 | **70%** |

## 🔑 إعداد APIs

### المطلوبة (أساسية)
```env
GEMINI_API_KEY=your_gemini_key_here
TELEGRAM_BOT_TOKEN=your_bot_token_here
```

### الاختيارية (للمميزات الإضافية)
```env
YOUTUBE_API_KEY=your_youtube_key_here
WHISPER_API_KEY=your_whisper_key_here
PEXELS_API_KEY=your_pexels_key_here
```

## 📱 التحكم عبر Telegram

### للجميع:
- 📊 **حالة النظام** - مراقبة الأداء والذاكرة
- 📰 **آخر الأخبار** - عرض المقالات المنشورة
- 📈 **الإحصائيات** - أرقام الاستخدام

### للمدير فقط:
- 🔑 **إدارة API Keys** - إضافة وتحديث المفاتيح
- 🚀 **تشغيل الوكيل** - بدء العمل
- ⏹️ **إيقاف الوكيل** - إيقاف آمن
- 🧹 **تنظيف الذاكرة** - تحرير الموارد

## 🌐 واجهة الويب

الوصول عبر: `http://your-app.onrender.com`

### المميزات:
- 📊 **لوحة تحكم** بسيطة وسريعة
- 💾 **مراقبة الذاكرة** في الوقت الفعلي
- 📈 **إحصائيات الأداء**
- 🔧 **إعدادات أساسية**

## 🛠️ استكشاف الأخطاء

### مشاكل الذاكرة
```bash
# فحص استهلاك الذاكرة
python -c "from modules.memory_manager_lite import memory_manager; print(memory_manager.get_stats())"

# تنظيف طوارئ
python -c "from modules.memory_manager_lite import memory_manager; memory_manager.force_emergency_cleanup()"
```

### مشاكل الشبكة
- تحقق من مفاتيح APIs
- راجع سجلات الأخطاء
- تأكد من اتصال الإنترنت

### مشاكل النشر
- تحقق من ملف `render.yaml`
- راجع متغيرات البيئة
- تأكد من `requirements.txt`

## 📈 المراقبة والصيانة

### مراقبة تلقائية:
- ✅ **استهلاك الذاكرة** كل دقيقة
- ✅ **تنظيف دوري** كل 5 دقائق
- ✅ **تحذيرات** عند 80% من الحد الأقصى
- ✅ **تنظيف طوارئ** عند 90%

### صيانة يدوية:
```bash
# إعادة تشغيل الوكيل
curl -X POST http://your-app.onrender.com/restart

# تنظيف الذاكرة
curl -X POST http://your-app.onrender.com/cleanup

# فحص الحالة
curl http://your-app.onrender.com/health
```

## 🎯 أفضل الممارسات

### للحصول على أفضل أداء:
1. **استخدم APIs خارجية** بدلاً من المعالجة المحلية
2. **راقب الذاكرة** باستمرار
3. **نظف الكاش** بانتظام
4. **تجنب المعالجة المتوازية** الثقيلة
5. **استخدم مهلة قصيرة** للطلبات

### للاستقرار:
1. **اختبر التحديثات** قبل النشر
2. **احتفظ بنسخة احتياطية** من الإعدادات
3. **راقب السجلات** للأخطاء
4. **استخدم معدل محدود** للطلبات

## 🆘 الدعم

### الحصول على المساعدة:
1. راجع ملف `test_lite_version.py` للتشخيص
2. تحقق من السجلات في `/logs`
3. استخدم بوت Telegram للحالة المباشرة
4. راجع واجهة الويب للإحصائيات

### الإبلاغ عن المشاكل:
- وصف المشكلة بالتفصيل
- أرفق سجلات الأخطاء
- اذكر استهلاك الذاكرة الحالي
- حدد المنصة المستخدمة

---

## 🎉 النتيجة

النسخة المحسنة تحافظ على **جميع المميزات الأساسية** مع تقليل استهلاك الموارد بنسبة **60-70%**، مما يجعلها مثالية للاستضافة المجانية!

**🚀 جاهز للنشر على Render مجاناً!**
