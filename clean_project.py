#!/usr/bin/env python3
"""
سكريبت تنظيف المشروع - حذف ملفات الاختبار والتوثيق الزائدة
"""
import os
import glob
import shutil

def clean_project():
    """تنظيف المشروع من الملفات غير المهمة"""
    
    # قائمة ملفات .md المراد حذفها (نحتفظ بـ README.md و LICENSE فقط)
    md_files_to_keep = ['README.md', 'LICENSE']
    
    # حذف جميع ملفات .md عدا المحددة
    for md_file in glob.glob('*.md'):
        if md_file not in md_files_to_keep:
            try:
                os.remove(md_file)
                print(f"تم حذف: {md_file}")
            except Exception as e:
                print(f"خطأ في حذف {md_file}: {e}")
    
    # حذف جميع ملفات test_*.py
    for test_file in glob.glob('test_*.py'):
        try:
            os.remove(test_file)
            print(f"تم حذف: {test_file}")
        except Exception as e:
            print(f"خطأ في حذف {test_file}: {e}")
    
    # حذف ملفات quick_test_*.py
    for quick_test_file in glob.glob('quick_test_*.py'):
        try:
            os.remove(quick_test_file)
            print(f"تم حذف: {quick_test_file}")
        except Exception as e:
            print(f"خطأ في حذف {quick_test_file}: {e}")
    
    # حذف ملفات التقارير JSON
    json_patterns = [
        '*_test_*.json',
        '*_report_*.json', 
        'api_*_report.json',
        'gaming_sites_diagnostic_report.json',
        'gemini_api_status_report.json'
    ]
    
    for pattern in json_patterns:
        for json_file in glob.glob(pattern):
            try:
                os.remove(json_file)
                print(f"تم حذف: {json_file}")
            except Exception as e:
                print(f"خطأ في حذف {json_file}: {e}")
    
    # حذف ملفات .bat غير المهمة
    bat_files_to_remove = [
        'START_INTELLIGENT_INDEXING.bat',
        'START_SEOWL_SYSTEM.bat', 
        'START_WHISPER.bat',
        'start_agent.bat',
        'start_clean.bat',
        'start_web.bat',
        'upload_to_github.bat',
        'upload_to_github_vandal324.bat'
    ]
    
    for bat_file in bat_files_to_remove:
        if os.path.exists(bat_file):
            try:
                os.remove(bat_file)
                print(f"تم حذف: {bat_file}")
            except Exception as e:
                print(f"خطأ في حذف {bat_file}: {e}")
    
    # حذف مجلدات غير مهمة
    folders_to_remove = [
        'cache',
        'temp', 
        'backups',
        '__pycache__',
        'test_results',
        'health_reports',
        'reports',
        'txtify_for_upload',
        'txtify_source'
    ]
    
    for folder in folders_to_remove:
        if os.path.exists(folder):
            try:
                shutil.rmtree(folder)
                print(f"تم حذف المجلد: {folder}")
            except Exception as e:
                print(f"خطأ في حذف المجلد {folder}: {e}")
    
    # حذف ملفات أخرى غير مهمة
    other_files_to_remove = [
        'todo.md',
        'client_secret.json',
        'project_info.json',
        'deployment_checklist.json',
        'improved_image_config.json',
        'smart_image_config_guide.json',
        'txtify_for_upload.tar.gz'
    ]
    
    for file in other_files_to_remove:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"تم حذف: {file}")
            except Exception as e:
                print(f"خطأ في حذف {file}: {e}")
    
    print("\n✅ تم تنظيف المشروع بنجاح!")
    print("الملفات المتبقية هي الملفات الأساسية فقط.")

if __name__ == "__main__":
    clean_project()
