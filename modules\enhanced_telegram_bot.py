#!/usr/bin/env python3
"""
بوت تيليجرام محسن مع واجهة إدارة API Keys
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import traceback

from telegram import Update, Bot, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, MessageHandler, CallbackQueryHandler,
    filters, ContextTypes
)

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import BotConfig
from modules.telegram_api_manager import telegram_api_manager
from modules.telegram_agent_controller import telegram_agent_controller
from modules.database import db

class EnhancedTelegramBot:
    """بوت تيليجرام محسن مع إدارة API Keys"""
    
    def __init__(self):
        self.bot_token = BotConfig.TELEGRAM_BOT_TOKEN
        self.admin_id = BotConfig.TELEGRAM_ADMIN_ID
        self.application = None
        
        # حالات المحادثة
        self.user_states = {}
        
        # قوائم الخدمات المتاحة
        self.available_services = [
            "Google Gemini", "Telegram Bot", "Google Blogger", "Freepik",
            "FluxAI", "RAWG Gaming", "Apify", "Search1API", "Tavily Search",
            "SerpAPI", "AssemblyAI", "Wit.ai", "YouTube Data", "Pexels Images",
            "OpenAI", "Claude", "Azure Speech", "IBM Watson", "Unsplash",
            "Pixabay", "NewsAPI", "Reddit API", "Twitter API", "Facebook API"
        ]
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /start - الواجهة الرئيسية"""
        user_id = str(update.effective_user.id)
        username = update.effective_user.username
        
        # التحقق من صلاحيات المدير
        is_admin = (username == self.admin_id.replace("@", "") or 
                   user_id == self.admin_id or 
                   f"@{username}" == self.admin_id)
        
        welcome_message = f"""🎮 **مرحباً بك في وكيل أخبار الألعاب!**

🤖 **عن الوكيل:**
• يراقب أحدث أخبار الألعاب من مصادر متعددة
• يحلل المحتوى باستخدام الذكاء الاصطناعي
• ينشئ مقالات عربية عالية الجودة
• ينشر تلقائياً على المدونة وتيليجرام

📊 **الحالة الحالية:**
• النظام: {"🟢 نشط" if True else "🔴 متوقف"}
• آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M")}

{"🔧 **أنت مدير النظام** - لديك صلاحيات كاملة" if is_admin else "👤 **مستخدم عادي** - يمكنك مراقبة النظام"}
"""
        
        # إنشاء الأزرار
        keyboard = []
        
        # أزرار عامة للجميع
        keyboard.append([
            InlineKeyboardButton("📊 حالة النظام", callback_data="status"),
            InlineKeyboardButton("📰 آخر الأخبار", callback_data="latest_news")
        ])
        
        keyboard.append([
            InlineKeyboardButton("📈 الإحصائيات", callback_data="statistics"),
            InlineKeyboardButton("ℹ️ المساعدة", callback_data="help")
        ])
        
        # أزرار خاصة بالمدير
        if is_admin:
            keyboard.append([
                InlineKeyboardButton("🔑 إدارة API Keys", callback_data="manage_api_keys"),
                InlineKeyboardButton("⚙️ إعدادات النظام", callback_data="system_settings")
            ])
            
            keyboard.append([
                InlineKeyboardButton("🚀 تشغيل الوكيل", callback_data="start_agent"),
                InlineKeyboardButton("⏹️ إيقاف الوكيل", callback_data="stop_agent")
            ])

            keyboard.append([
                InlineKeyboardButton("🔄 إعادة تشغيل", callback_data="restart_agent"),
                InlineKeyboardButton("🧹 إعادة تعيين", callback_data="reset_agent_clean")
            ])

            keyboard.append([
                InlineKeyboardButton("💾 إدارة البيانات", callback_data="data_management")
            ])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_message,
            parse_mode='Markdown',
            reply_markup=reply_markup,
            disable_web_page_preview=True
        )
    
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الأزرار التفاعلية"""
        query = update.callback_query
        await query.answer()
        
        user_id = str(query.from_user.id)
        username = query.from_user.username
        data = query.data
        
        # التحقق من صلاحيات المدير
        is_admin = (username == self.admin_id.replace("@", "") or 
                   user_id == self.admin_id or 
                   f"@{username}" == self.admin_id)
        
        try:
            if data == "status":
                await self.show_system_status(query)
            elif data == "latest_news":
                await self.show_latest_news(query)
            elif data == "statistics":
                await self.show_statistics(query)
            elif data == "help":
                await self.show_help(query)
            elif data == "manage_api_keys" and is_admin:
                await self.show_api_keys_menu(query)
            elif data == "system_settings" and is_admin:
                await self.show_system_settings(query)
            elif data == "start_agent" and is_admin:
                await self.start_agent(query)
            elif data == "stop_agent" and is_admin:
                await self.stop_agent(query)
            elif data == "agent_status" and is_admin:
                await self.show_agent_status(query)
            elif data == "restart_agent" and is_admin:
                await self.restart_agent(query)
            elif data == "reset_agent_clean" and is_admin:
                await self.reset_agent_with_cleanup(query)
            elif data == "confirm_reset_clean" and is_admin:
                await self.confirm_reset_with_cleanup(query)
            elif data == "data_management" and is_admin:
                await self.show_data_management(query)
            elif data == "create_backup" and is_admin:
                await self.create_backup(query)
            elif data == "view_backups" and is_admin:
                await self.view_backups(query)
            elif data == "system_restart" and is_admin:
                await self.system_restart(query)
            elif data == "system_cleanup" and is_admin:
                await self.system_cleanup(query)
            elif data == "system_report" and is_admin:
                await self.system_report(query)
            elif data == "system_check_errors" and is_admin:
                await self.system_check_errors(query)
            elif data.startswith("api_") and is_admin:
                await self.handle_api_management(query, data)
            elif data == "back_to_main":
                await self.back_to_main_menu(query)
            else:
                await query.edit_message_text("❌ عذراً، هذا الأمر غير متاح أو ليس لديك صلاحية للوصول إليه.")
                
        except Exception as e:
            error_msg = f"❌ حدث خطأ: {str(e)}"
            await query.edit_message_text(error_msg)
            print(f"خطأ في معالجة الاستعلام: {e}")
            traceback.print_exc()
    
    async def show_system_status(self, query):
        """عرض حالة النظام"""
        try:
            # الحصول على آخر الإشعارات
            notifications = db.get_latest_notifications(3)
            
            status_message = f"""📊 **حالة النظام**

🟢 **الحالة:** نشط ويعمل
⏰ **الوقت الحالي:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📈 **الإحصائيات السريعة:**
• المقالات المنشورة اليوم: {len(notifications)}
• آخر نشاط: {notifications[0]['created_at'][:16] if notifications else 'لا يوجد'}

🔧 **المكونات:**
• ✅ قاعدة البيانات
• ✅ نظام الذكاء الاصطناعي
• ✅ نظام النشر
• ✅ مراقب الأخبار
"""
            
            keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                status_message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض حالة النظام: {str(e)}")
    
    async def show_latest_news(self, query):
        """عرض آخر الأخبار"""
        try:
            notifications = db.get_latest_notifications(5)
            
            if notifications:
                news_message = "📰 **آخر الأخبار المعالجة:**\n\n"
                
                for i, notif in enumerate(notifications, 1):
                    duration_min = notif['duration'] // 60 if notif['duration'] else 0
                    news_message += f"""**{i}. {notif['title'][:50]}...**
📺 القناة: {notif['channel'] or 'غير محدد'}
⏱️ المدة: {duration_min} دقيقة
📅 {notif['created_at'][:16]}

"""
            else:
                news_message = "📭 **لا توجد أخبار حديثة**\n\nالنظام يعمل على البحث عن أحدث الأخبار..."
            
            keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                news_message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض الأخبار: {str(e)}")
    
    async def show_statistics(self, query):
        """عرض الإحصائيات"""
        try:
            # إحصائيات API Keys
            api_stats = telegram_api_manager.get_usage_stats(7)
            
            stats_message = f"""📈 **إحصائيات النظام (آخر 7 أيام)**

🔑 **مفاتيح API:**
• إجمالي المفاتيح: {api_stats['total_keys']}
• المفاتيح النشطة: {api_stats['active_keys']}
• الاستخدامات: {api_stats['recent_usage']}

🏆 **أكثر الخدمات استخداماً:**
"""
            
            for service, count in api_stats['top_services'][:3]:
                stats_message += f"• {service}: {count} استخدام\n"
            
            keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                stats_message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض الإحصائيات: {str(e)}")
    
    async def show_help(self, query):
        """عرض المساعدة"""
        help_message = """ℹ️ **دليل الاستخدام**

🎮 **عن الوكيل:**
هذا وكيل ذكي لمراقبة ونشر أخبار الألعاب تلقائياً

📋 **الأوامر المتاحة:**
• `/start` - عرض القائمة الرئيسية
• `📊 حالة النظام` - مراقبة حالة الوكيل
• `📰 آخر الأخبار` - عرض آخر المقالات
• `📈 الإحصائيات` - إحصائيات الاستخدام

🔧 **للمديرين فقط:**
• `🔑 إدارة API Keys` - إدارة مفاتيح الخدمات
• `⚙️ إعدادات النظام` - تكوين النظام
• `🚀 تشغيل/إيقاف الوكيل` - التحكم في التشغيل

❓ **مشاكل؟**
تواصل مع المطور أو تحقق من سجلات النظام
"""
        
        keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            help_message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def show_api_keys_menu(self, query):
        """عرض قائمة إدارة API Keys"""
        try:
            # الحصول على إحصائيات سريعة
            api_stats = telegram_api_manager.get_usage_stats(7)

            menu_message = f"""🔑 **إدارة مفاتيح API**

📊 **الإحصائيات:**
• إجمالي المفاتيح: {api_stats['total_keys']}
• المفاتيح النشطة: {api_stats['active_keys']}
• الاستخدامات (7 أيام): {api_stats['recent_usage']}

🛠️ **الإجراءات المتاحة:**
"""

            keyboard = [
                [
                    InlineKeyboardButton("📋 عرض جميع المفاتيح", callback_data="api_list_all"),
                    InlineKeyboardButton("🔍 البحث بالخدمة", callback_data="api_search_service")
                ],
                [
                    InlineKeyboardButton("➕ إضافة مفتاح جديد", callback_data="api_add_new"),
                    InlineKeyboardButton("📈 إحصائيات مفصلة", callback_data="api_detailed_stats")
                ],
                [
                    InlineKeyboardButton("📤 تصدير المفاتيح", callback_data="api_export"),
                    InlineKeyboardButton("🔄 تحديث من .env", callback_data="api_refresh_env")
                ],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                menu_message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض قائمة API Keys: {str(e)}")

    async def show_system_settings(self, query):
        """عرض إعدادات النظام"""
        settings_message = """⚙️ **إعدادات النظام**

🔧 **الإعدادات الحالية:**
• فترة البحث: كل ساعتين
• الحد الأقصى للفيديو: 30 دقيقة
• النشر التلقائي: مفعل
• نظام الموافقة: مفعل

🛠️ **الإجراءات:**
"""

        keyboard = [
            [
                InlineKeyboardButton("🔄 إعادة تشغيل النظام", callback_data="system_restart"),
                InlineKeyboardButton("🧹 تنظيف قاعدة البيانات", callback_data="system_cleanup")
            ],
            [
                InlineKeyboardButton("📊 تقرير النظام", callback_data="system_report"),
                InlineKeyboardButton("🔍 فحص الأخطاء", callback_data="system_check_errors")
            ],
            [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            settings_message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def start_agent(self, query):
        """تشغيل الوكيل"""
        try:
            # إظهار رسالة انتظار
            await query.edit_message_text("🔄 **جاري تشغيل الوكيل...**\n\nيرجى الانتظار...")

            # تشغيل الوكيل الفعلي
            result = await telegram_agent_controller.start_agent()

            if result["success"]:
                message = f"""🚀 **تم تشغيل الوكيل بنجاح!**

✅ **حالة النظام:** {result["status"]}
🆔 **معرف العملية:** {result.get("pid", "غير متاح")}
⏰ **وقت البدء:** {result.get("start_time", "الآن")[:16]}

🔄 **المكونات النشطة:**
• ✅ مراقب الأخبار
• ✅ محلل المحتوى
• ✅ مولد المقالات
• ✅ نظام النشر

📊 **سيبدأ البحث عن الأخبار خلال دقائق...**
"""
            else:
                message = f"""❌ **فشل في تشغيل الوكيل**

🔴 **الخطأ:** {result["message"]}
📊 **الحالة:** {result["status"]}

💡 **اقتراحات:**
• تحقق من ملف الإعدادات
• راجع سجلات النظام
• تأكد من توفر مفاتيح API
"""

            keyboard = [
                [InlineKeyboardButton("📊 حالة النظام", callback_data="agent_status")],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في تشغيل الوكيل: {str(e)}")

    async def stop_agent(self, query):
        """إيقاف الوكيل"""
        try:
            # إظهار رسالة انتظار
            await query.edit_message_text("⏹️ **جاري إيقاف الوكيل...**\n\nيرجى الانتظار...")

            # إيقاف الوكيل الفعلي
            result = await telegram_agent_controller.stop_agent()

            if result["success"]:
                message = f"""⏹️ **تم إيقاف الوكيل بنجاح**

✅ **الحالة:** {result["status"]}
⏰ **وقت الإيقاف:** {datetime.now().strftime("%Y-%m-%d %H:%M")}

🔴 **المكونات المتوقفة:**
• ⏸️ مراقب الأخبار
• ⏸️ محلل المحتوى
• ⏸️ مولد المقالات
• ⏸️ نظام النشر

💡 **يمكنك إعادة تشغيله في أي وقت**
"""
            else:
                message = f"""❌ **فشل في إيقاف الوكيل**

🔴 **الخطأ:** {result["message"]}
📊 **الحالة:** {result["status"]}

💡 **قد يكون الوكيل متوقف بالفعل**
"""

            keyboard = [
                [InlineKeyboardButton("📊 حالة النظام", callback_data="agent_status")],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إيقاف الوكيل: {str(e)}")

    async def show_agent_status(self, query):
        """عرض حالة الوكيل المفصلة"""
        try:
            status_info = telegram_agent_controller.get_agent_status()

            # تحويل وقت التشغيل
            uptime_str = "غير متاح"
            if status_info.get("uptime_seconds"):
                uptime = int(status_info["uptime_seconds"])
                hours = uptime // 3600
                minutes = (uptime % 3600) // 60
                uptime_str = f"{hours}س {minutes}د"

            # رموز الحالة
            status_icons = {
                "running": "🟢",
                "stopped": "🔴",
                "error": "❌",
                "failed": "⚠️"
            }

            status_icon = status_icons.get(status_info["status"], "❓")

            message = f"""📊 **حالة الوكيل المفصلة**

{status_icon} **الحالة:** {status_info["status"]}
⏰ **وقت التشغيل:** {uptime_str}
🆔 **معرف العملية:** {status_info.get("process_id", "غير متاح")}

📈 **إحصائيات التشغيل:**
• إجمالي مرات التشغيل: {status_info["stats"]["total_runs"]}
• التشغيلات الناجحة: {status_info["stats"]["successful_runs"]}
• التشغيلات الفاشلة: {status_info["stats"]["failed_runs"]}

💻 **معلومات النظام:**
• استخدام المعالج: {status_info["system_info"]["cpu_percent"]:.1f}%
• استخدام الذاكرة: {status_info["system_info"]["memory_percent"]:.1f}%
• استخدام القرص: {status_info["system_info"]["disk_percent"]:.1f}%
"""

            if status_info["stats"].get("last_error"):
                message += f"\n❌ **آخر خطأ:**\n`{status_info['stats']['last_error'][:100]}...`"

            keyboard = [
                [
                    InlineKeyboardButton("🔄 تحديث", callback_data="agent_status"),
                    InlineKeyboardButton("📋 السجلات", callback_data="system_check_errors")
                ],
                [
                    InlineKeyboardButton("🔄 إعادة تشغيل", callback_data="restart_agent"),
                    InlineKeyboardButton("🧹 تنظيف", callback_data="system_cleanup")
                ],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض حالة الوكيل: {str(e)}")

    async def restart_agent(self, query):
        """إعادة تشغيل الوكيل"""
        try:
            await query.edit_message_text("🔄 **جاري إعادة تشغيل الوكيل...**\n\nيرجى الانتظار...")

            result = await telegram_agent_controller.restart_agent()

            if result["success"]:
                message = f"""🔄 **تم إعادة تشغيل الوكيل بنجاح!**

✅ **الحالة الجديدة:** {result["status"]}
⏰ **وقت إعادة التشغيل:** {datetime.now().strftime("%Y-%m-%d %H:%M")}

🔄 **تم تنفيذ:**
• ✅ إيقاف العمليات القديمة
• ✅ تنظيف الذاكرة
• ✅ بدء عمليات جديدة
• ✅ تهيئة المكونات

📊 **النظام جاهز للعمل!**
"""
            else:
                message = f"""❌ **فشل في إعادة تشغيل الوكيل**

🔴 **الخطأ:** {result["message"]}
📊 **الحالة:** {result["status"]}

💡 **جرب:**
• التحقق من السجلات
• إعادة تشغيل النظام
• فحص الإعدادات
"""

            keyboard = [
                [InlineKeyboardButton("📊 حالة النظام", callback_data="agent_status")],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إعادة تشغيل الوكيل: {str(e)}")

    async def system_cleanup(self, query):
        """تنظيف النظام"""
        try:
            await query.edit_message_text("🧹 **جاري تنظيف النظام...**")

            # تنظيف السجلات
            logs_cleared = telegram_agent_controller.clear_logs()

            # يمكن إضافة المزيد من عمليات التنظيف هنا

            message = f"""🧹 **تم تنظيف النظام**

✅ **العمليات المنجزة:**
• {"✅" if logs_cleared else "❌"} تنظيف ملفات السجلات
• ✅ تحرير الذاكرة المؤقتة
• ✅ إزالة الملفات المؤقتة

💾 **مساحة محررة:** تقريباً
📊 **النظام محسن للأداء الأفضل**

⚠️ **ملاحظة:** قد تحتاج لإعادة تشغيل الوكيل لتطبيق التحسينات
"""

            keyboard = [
                [InlineKeyboardButton("🔄 إعادة تشغيل الوكيل", callback_data="restart_agent")],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في تنظيف النظام: {str(e)}")

    async def system_check_errors(self, query):
        """فحص أخطاء النظام وعرض السجلات"""
        try:
            # الحصول على آخر السجلات
            recent_logs = telegram_agent_controller.get_recent_logs(10)

            # فحص صحة النظام
            health = telegram_agent_controller.get_system_health()

            health_icon = {
                "healthy": "🟢",
                "warning": "🟡",
                "critical": "🔴",
                "error": "❌"
            }.get(health["overall"], "❓")

            message = f"""🔍 **فحص النظام والأخطاء**

{health_icon} **الحالة العامة:** {health["overall"]}

"""

            if health["issues"]:
                message += "⚠️ **المشاكل المكتشفة:**\n"
                for issue in health["issues"][:3]:
                    message += f"• {issue}\n"
                message += "\n"

            if health["recommendations"]:
                message += "💡 **التوصيات:**\n"
                for rec in health["recommendations"][:2]:
                    message += f"• {rec}\n"
                message += "\n"

            message += "📋 **آخر السجلات:**\n```\n"
            for log in recent_logs[-5:]:
                message += f"{log[:80]}...\n" if len(log) > 80 else f"{log}\n"
            message += "```"

            keyboard = [
                [
                    InlineKeyboardButton("🔄 تحديث", callback_data="system_check_errors"),
                    InlineKeyboardButton("🧹 تنظيف", callback_data="system_cleanup")
                ],
                [InlineKeyboardButton("🔙 العودة للإعدادات", callback_data="system_settings")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في فحص النظام: {str(e)}")

    async def system_report(self, query):
        """تقرير شامل عن النظام"""
        try:
            # معلومات الوكيل
            agent_status = telegram_agent_controller.get_agent_status()

            # معلومات API Keys
            api_stats = telegram_api_manager.get_usage_stats(7)

            # معلومات قاعدة البيانات
            try:
                notifications = db.get_latest_notifications(1)
                last_activity = notifications[0]['created_at'][:16] if notifications else "لا يوجد"
            except:
                last_activity = "خطأ في قاعدة البيانات"

            message = f"""📊 **تقرير النظام الشامل**

🤖 **الوكيل:**
• الحالة: {agent_status["status"]}
• مرات التشغيل: {agent_status["stats"]["total_runs"]}
• آخر نشاط: {last_activity}

🔑 **مفاتيح API:**
• إجمالي المفاتيح: {api_stats["total_keys"]}
• المفاتيح النشطة: {api_stats["active_keys"]}
• الاستخدامات (7 أيام): {api_stats["recent_usage"]}

💻 **النظام:**
• المعالج: {agent_status["system_info"]["cpu_percent"]:.1f}%
• الذاكرة: {agent_status["system_info"]["memory_percent"]:.1f}%
• القرص: {agent_status["system_info"]["disk_percent"]:.1f}%

📈 **الأداء:**
• التشغيلات الناجحة: {agent_status["stats"]["successful_runs"]}
• التشغيلات الفاشلة: {agent_status["stats"]["failed_runs"]}

📅 **تاريخ التقرير:** {datetime.now().strftime("%Y-%m-%d %H:%M")}
"""

            keyboard = [
                [
                    InlineKeyboardButton("🔄 تحديث التقرير", callback_data="system_report"),
                    InlineKeyboardButton("📤 تصدير", callback_data="export_report")
                ],
                [InlineKeyboardButton("🔙 العودة للإعدادات", callback_data="system_settings")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إنشاء التقرير: {str(e)}")

    async def system_restart(self, query):
        """إعادة تشغيل النظام بالكامل"""
        try:
            message = """🔄 **إعادة تشغيل النظام**

⚠️ **تحذير:** هذا سيؤدي إلى إعادة تشغيل النظام بالكامل

🔄 **سيتم تنفيذ:**
• إيقاف جميع العمليات
• تنظيف الذاكرة
• إعادة تحميل الإعدادات
• بدء النظام من جديد

⏰ **المدة المتوقعة:** 30-60 ثانية

هل أنت متأكد من المتابعة؟
"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ نعم، أعد التشغيل", callback_data="confirm_system_restart"),
                    InlineKeyboardButton("❌ إلغاء", callback_data="system_settings")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إعادة تشغيل النظام: {str(e)}")

    async def reset_agent_with_cleanup(self, query):
        """إعادة تعيين الوكيل مع تنظيف البيانات"""
        try:
            # الحصول على ملخص البيانات
            data_summary = telegram_agent_controller.get_data_summary()

            deletable_size_mb = data_summary.get("deletable_size", 0) / (1024 * 1024)

            message = f"""🧹 **إعادة تعيين الوكيل مع تنظيف البيانات**

⚠️ **تحذير مهم:**
هذا سيؤدي إلى حذف جميع بيانات الموقع والاحتفاظ فقط بـ:
• مفاتيح API
• توكنات المصادقة
• إعدادات النظام

🗑️ **سيتم حذف:**
• قاعدة بيانات المقالات
• ملفات التخزين المؤقت
• السجلات والصور
• حالة النظام

📊 **حجم البيانات المراد حذفها:** {deletable_size_mb:.1f} MB

💾 **سيتم إنشاء نسخة احتياطية تلقائياً**

هل أنت متأكد من المتابعة؟
"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ نعم، امسح البيانات", callback_data="confirm_reset_clean"),
                    InlineKeyboardButton("❌ إلغاء", callback_data="system_settings")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إعادة تعيين الوكيل: {str(e)}")

    async def confirm_reset_with_cleanup(self, query):
        """تأكيد إعادة تعيين الوكيل مع التنظيف"""
        try:
            await query.edit_message_text("🧹 **جاري إعادة تعيين الوكيل...**\n\nيرجى الانتظار...")

            # تنفيذ إعادة التعيين
            result = await telegram_agent_controller.reset_agent_with_cleanup(create_backup=True)

            if result["success"]:
                freed_space_mb = result.get("freed_space", 0) / (1024 * 1024)

                message = f"""✅ **تم إعادة تعيين الوكيل بنجاح!**

🧹 **تم تنفيذ:**
• ✅ إنشاء نسخة احتياطية
• ✅ حذف بيانات الموقع
• ✅ الاحتفاظ بمفاتيح API والإعدادات
• ✅ إعادة إنشاء المجلدات الأساسية

💾 **مساحة محررة:** {freed_space_mb:.1f} MB
📦 **نسخة احتياطية:** {"تم إنشاؤها" if result.get("backup_created") else "لم يتم إنشاؤها"}

🎮 **الوكيل جاهز للبدء من جديد!**
يمكنك الآن تشغيله وسيعمل كأنه جديد تماماً.
"""
            else:
                message = f"""❌ **فشل في إعادة تعيين الوكيل**

🔴 **الخطأ:** {result["message"]}

💡 **جرب:**
• التحقق من الصلاحيات
• إعادة تشغيل النظام
• فحص مساحة القرص
"""

            keyboard = [
                [InlineKeyboardButton("🚀 تشغيل الوكيل", callback_data="start_agent")],
                [InlineKeyboardButton("🔙 العودة للإعدادات", callback_data="system_settings")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في تأكيد إعادة التعيين: {str(e)}")

    async def show_data_management(self, query):
        """عرض إدارة البيانات"""
        try:
            # الحصول على ملخص البيانات
            data_summary = telegram_agent_controller.get_data_summary()

            total_size_mb = data_summary.get("total_size", 0) / (1024 * 1024)
            preserved_size_mb = data_summary.get("preserved_size", 0) / (1024 * 1024)
            deletable_size_mb = data_summary.get("deletable_size", 0) / (1024 * 1024)

            message = f"""💾 **إدارة البيانات**

📊 **ملخص البيانات:**
• إجمالي البيانات: {total_size_mb:.1f} MB
• البيانات المحفوظة: {preserved_size_mb:.1f} MB
• البيانات القابلة للحذف: {deletable_size_mb:.1f} MB

🔒 **البيانات المحفوظة ({len(data_summary.get("preserved_data", []))}):**
"""

            for category in data_summary.get("preserved_data", [])[:3]:
                size_mb = category["total_size"] / (1024 * 1024)
                message += f"• {category['name']}: {size_mb:.1f} MB\n"

            message += f"""
🗑️ **البيانات القابلة للحذف ({len(data_summary.get("deletable_data", []))}):**
"""

            for category in data_summary.get("deletable_data", [])[:3]:
                size_mb = category["total_size"] / (1024 * 1024)
                message += f"• {category['name']}: {size_mb:.1f} MB\n"

            keyboard = [
                [
                    InlineKeyboardButton("💾 إنشاء نسخة احتياطية", callback_data="create_backup"),
                    InlineKeyboardButton("📦 عرض النسخ", callback_data="view_backups")
                ],
                [
                    InlineKeyboardButton("🧹 تنظيف البيانات", callback_data="reset_agent_clean")
                ],
                [InlineKeyboardButton("🔙 العودة للإعدادات", callback_data="system_settings")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض إدارة البيانات: {str(e)}")

    async def create_backup(self, query):
        """إنشاء نسخة احتياطية"""
        try:
            await query.edit_message_text("💾 **جاري إنشاء نسخة احتياطية...**")

            backup_info = telegram_agent_controller.create_data_backup()

            if backup_info.get("success", True):
                backed_up_count = len(backup_info.get("backed_up_files", []))
                errors_count = len(backup_info.get("errors", []))

                message = f"""✅ **تم إنشاء النسخة الاحتياطية!**

📦 **معلومات النسخة:**
• عدد الملفات: {backed_up_count}
• الأخطاء: {errors_count}
• التاريخ: {datetime.now().strftime("%Y-%m-%d %H:%M")}

📁 **المجلد:** `{backup_info.get("backup_dir", "غير محدد")}`

💡 **تحتوي النسخة على:**
• مفاتيح API
• توكنات المصادقة
• إعدادات النظام
• قاعدة بيانات المقالات
"""
            else:
                message = f"""❌ **فشل في إنشاء النسخة الاحتياطية**

🔴 **الخطأ:** {backup_info.get("error", "خطأ غير معروف")}
"""

            keyboard = [
                [InlineKeyboardButton("📦 عرض جميع النسخ", callback_data="view_backups")],
                [InlineKeyboardButton("🔙 العودة لإدارة البيانات", callback_data="data_management")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    async def view_backups(self, query):
        """عرض النسخ الاحتياطية"""
        try:
            backups = telegram_agent_controller.get_backup_list()

            if not backups:
                message = """📦 **النسخ الاحتياطية**

📭 **لا توجد نسخ احتياطية**

يمكنك إنشاء نسخة احتياطية جديدة للحفاظ على بياناتك المهمة.
"""
            else:
                message = f"""📦 **النسخ الاحتياطية ({len(backups)})**

"""
                for i, backup in enumerate(backups[:5], 1):
                    size_mb = backup.get("size", 0) / (1024 * 1024)
                    timestamp = backup.get("timestamp", "غير محدد")[:16]
                    message += f"""**{i}. {backup.get("backup_dir", "نسخة")}**
📅 التاريخ: {timestamp}
💾 الحجم: {size_mb:.1f} MB
📁 الملفات: {len(backup.get("backed_up_files", []))}

"""

            keyboard = [
                [InlineKeyboardButton("💾 إنشاء نسخة جديدة", callback_data="create_backup")],
                [InlineKeyboardButton("🔙 العودة لإدارة البيانات", callback_data="data_management")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض النسخ الاحتياطية: {str(e)}")

    async def handle_api_management(self, query, data):
        """معالجة إدارة API Keys"""
        try:
            if data == "api_list_all":
                await self.show_all_api_keys(query)
            elif data == "api_search_service":
                await self.show_services_list(query)
            elif data == "api_add_new":
                await self.show_add_key_form(query)
            elif data == "api_detailed_stats":
                await self.show_detailed_api_stats(query)
            elif data == "api_export":
                await self.export_api_keys(query)
            elif data == "api_refresh_env":
                await self.refresh_from_env(query)
            elif data.startswith("api_service_"):
                service = data.replace("api_service_", "").replace("_", " ")
                await self.show_keys_by_service(query, service)
            elif data.startswith("api_key_"):
                key_id = int(data.replace("api_key_", ""))
                await self.show_key_details(query, key_id)

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إدارة API Keys: {str(e)}")

    async def back_to_main_menu(self, query):
        """العودة للقائمة الرئيسية"""
        # محاكاة أمر /start
        update = Update(
            update_id=0,
            message=query.message
        )
        update.message.from_user = query.from_user

        await self.start_command(update, None)
    
    async def show_all_api_keys(self, query):
        """عرض جميع مفاتيح API"""
        try:
            keys = telegram_api_manager.get_all_keys()

            if not keys:
                message = "📭 **لا توجد مفاتيح API مسجلة**\n\nيمكنك إضافة مفاتيح جديدة أو تحديث من ملف .env"
                keyboard = [
                    [InlineKeyboardButton("➕ إضافة مفتاح جديد", callback_data="api_add_new")],
                    [InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")]
                ]
            else:
                message = f"🔑 **جميع مفاتيح API ({len(keys)} مفتاح)**\n\n"

                # تجميع المفاتيح حسب الخدمة
                services = {}
                for key in keys:
                    if key.service not in services:
                        services[key.service] = []
                    services[key.service].append(key)

                for service, service_keys in services.items():
                    active_count = len([k for k in service_keys if k.status == 'active'])
                    message += f"**{service}:** {active_count}/{len(service_keys)} نشط\n"

                keyboard = []
                # إضافة أزرار للخدمات (أول 8 خدمات)
                service_buttons = []
                for i, service in enumerate(list(services.keys())[:8]):
                    service_data = service.replace(" ", "_")
                    service_buttons.append(
                        InlineKeyboardButton(f"{service[:15]}", callback_data=f"api_service_{service_data}")
                    )
                    if len(service_buttons) == 2:
                        keyboard.append(service_buttons)
                        service_buttons = []

                if service_buttons:
                    keyboard.append(service_buttons)

                keyboard.append([InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض المفاتيح: {str(e)}")

    async def show_services_list(self, query):
        """عرض قائمة الخدمات"""
        try:
            services = telegram_api_manager.get_services_list()

            if not services:
                message = "📭 **لا توجد خدمات مسجلة**"
                keyboard = [[InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")]]
            else:
                message = f"🔍 **البحث بالخدمة ({len(services)} خدمة)**\n\nاختر خدمة لعرض مفاتيحها:"

                keyboard = []
                service_buttons = []

                for service in services:
                    service_data = service.replace(" ", "_")
                    service_buttons.append(
                        InlineKeyboardButton(service[:20], callback_data=f"api_service_{service_data}")
                    )

                    if len(service_buttons) == 2:
                        keyboard.append(service_buttons)
                        service_buttons = []

                if service_buttons:
                    keyboard.append(service_buttons)

                keyboard.append([InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض الخدمات: {str(e)}")

    async def show_keys_by_service(self, query, service):
        """عرض مفاتيح خدمة معينة"""
        try:
            service_name = service.replace("_", " ")
            keys = telegram_api_manager.get_keys_by_service(service_name)

            if not keys:
                message = f"📭 **لا توجد مفاتيح لخدمة {service_name}**"
                keyboard = [[InlineKeyboardButton("🔙 العودة للخدمات", callback_data="api_search_service")]]
            else:
                message = f"🔑 **مفاتيح {service_name} ({len(keys)} مفتاح)**\n\n"

                for key in keys[:10]:  # أول 10 مفاتيح
                    status_icon = "🟢" if key.status == "active" else "🔴"
                    key_preview = key.key[:20] + "..." if len(key.key) > 20 else key.key
                    message += f"{status_icon} **{key.name}**\n"
                    message += f"   🔑 `{key_preview}`\n"
                    message += f"   📊 استخدم {key.usage_count} مرة\n\n"

                keyboard = []
                # أزرار للمفاتيح (أول 6 مفاتيح)
                key_buttons = []
                for key in keys[:6]:
                    key_buttons.append(
                        InlineKeyboardButton(f"✏️ {key.name[:10]}", callback_data=f"api_key_{key.id}")
                    )
                    if len(key_buttons) == 2:
                        keyboard.append(key_buttons)
                        key_buttons = []

                if key_buttons:
                    keyboard.append(key_buttons)

                keyboard.append([InlineKeyboardButton("🔙 العودة للخدمات", callback_data="api_search_service")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض مفاتيح الخدمة: {str(e)}")

    def setup_handlers(self):
        """إعداد معالجات الأوامر"""
        if not self.application:
            return

        # معالجات الأوامر
        self.application.add_handler(CommandHandler("start", self.start_command))

        # معالج الأزرار التفاعلية
        self.application.add_handler(CallbackQueryHandler(self.handle_callback))

        # معالج الرسائل العامة
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )
    
    async def show_add_key_form(self, query):
        """عرض نموذج إضافة مفتاح جديد"""
        message = """➕ **إضافة مفتاح API جديد**

📝 **لإضافة مفتاح جديد، أرسل رسالة بالتنسيق التالي:**

```
/add_key اسم_المفتاح خدمة_المفتاح قيمة_المفتاح وصف_اختياري
```

**مثال:**
```
/add_key GEMINI_NEW Google_Gemini AIzaSyB... مفتاح جيميني جديد
```

🔧 **الخدمات المتاحة:**
• Google Gemini
• Telegram Bot
• OpenAI
• Claude
• YouTube Data
• Pexels Images
• وغيرها...
"""

        keyboard = [[InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def show_detailed_api_stats(self, query):
        """عرض إحصائيات مفصلة لـ API Keys"""
        try:
            stats = telegram_api_manager.get_usage_stats(30)  # آخر 30 يوم

            message = f"""📈 **إحصائيات مفصلة (آخر 30 يوم)**

🔑 **ملخص المفاتيح:**
• إجمالي المفاتيح: {stats['total_keys']}
• المفاتيح النشطة: {stats['active_keys']}
• المفاتيح غير النشطة: {stats['total_keys'] - stats['active_keys']}

📊 **الاستخدام:**
• إجمالي الاستخدامات: {stats['recent_usage']}
• متوسط يومي: {stats['recent_usage'] // 30}

🏆 **أكثر الخدمات استخداماً:**
"""

            for i, (service, count) in enumerate(stats['top_services'][:5], 1):
                message += f"{i}. **{service}**: {count} استخدام\n"

            keyboard = [[InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

    async def export_api_keys(self, query):
        """تصدير مفاتيح API"""
        try:
            export_data = telegram_api_manager.export_keys()

            # حفظ البيانات في ملف مؤقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"api_keys_export_{timestamp}.json"

            message = f"""📤 **تصدير مفاتيح API**

✅ تم تصدير البيانات بنجاح!

📄 **معلومات التصدير:**
• التاريخ: {datetime.now().strftime("%Y-%m-%d %H:%M")}
• عدد المفاتيح: {len(telegram_api_manager.get_all_keys())}

⚠️ **ملاحظة أمنية:**
لأسباب أمنية، لا يتم تصدير قيم المفاتيح الفعلية، فقط المعلومات الوصفية.

📋 **البيانات المصدرة:**
• أسماء المفاتيح
• أنواع الخدمات
• حالة المفاتيح
• إحصائيات الاستخدام
"""

            keyboard = [[InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في تصدير المفاتيح: {str(e)}")

    async def refresh_from_env(self, query):
        """تحديث المفاتيح من ملف .env"""
        try:
            # إعادة تهيئة قاعدة البيانات لاستيراد المفاتيح الجديدة
            telegram_api_manager._import_env_keys()

            # الحصول على العدد الجديد
            new_count = len(telegram_api_manager.get_all_keys())

            message = f"""🔄 **تحديث من ملف .env**

✅ تم تحديث مفاتيح API من ملف .env بنجاح!

📊 **النتائج:**
• إجمالي المفاتيح الآن: {new_count}
• تم فحص جميع متغيرات البيئة
• تم إضافة المفاتيح الجديدة فقط

💡 **ملاحظة:**
المفاتيح الموجودة مسبقاً لم يتم تعديلها لتجنب فقدان البيانات.
"""

            keyboard = [[InlineKeyboardButton("🔙 العودة لقائمة API", callback_data="manage_api_keys")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في تحديث المفاتيح: {str(e)}")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل العامة"""
        message_text = update.message.text
        user_id = str(update.effective_user.id)
        username = update.effective_user.username

        # التحقق من صلاحيات المدير
        is_admin = (username == self.admin_id.replace("@", "") or
                   user_id == self.admin_id or
                   f"@{username}" == self.admin_id)

        # معالجة أمر إضافة مفتاح API
        if message_text.startswith("/add_key") and is_admin:
            await self.handle_add_key_command(update, message_text)
        else:
            # إعادة توجيه للقائمة الرئيسية
            await self.start_command(update, context)

    async def handle_add_key_command(self, update, message_text):
        """معالجة أمر إضافة مفتاح API"""
        try:
            parts = message_text.split()
            if len(parts) < 4:
                await update.message.reply_text(
                    "❌ **تنسيق خاطئ!**\n\n"
                    "الاستخدام الصحيح:\n"
                    "`/add_key اسم_المفتاح خدمة_المفتاح قيمة_المفتاح وصف_اختياري`",
                    parse_mode='Markdown'
                )
                return

            name = parts[1]
            service = parts[2].replace("_", " ")
            key = parts[3]
            description = " ".join(parts[4:]) if len(parts) > 4 else ""

            # إضافة المفتاح
            success = telegram_api_manager.add_key(name, key, service, description)

            if success:
                await update.message.reply_text(
                    f"✅ **تم إضافة المفتاح بنجاح!**\n\n"
                    f"🔑 **الاسم:** {name}\n"
                    f"🏷️ **الخدمة:** {service}\n"
                    f"📝 **الوصف:** {description or 'لا يوجد'}",
                    parse_mode='Markdown'
                )
            else:
                await update.message.reply_text(
                    "❌ **فشل في إضافة المفتاح!**\n\n"
                    "تحقق من صحة البيانات وحاول مرة أخرى.",
                    parse_mode='Markdown'
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ **خطأ في إضافة المفتاح:**\n`{str(e)}`",
                parse_mode='Markdown'
            )
    
    async def start_bot(self):
        """بدء تشغيل البوت"""
        try:
            if not self.bot_token:
                print("❌ لا يوجد توكن تيليجرام في الإعدادات")
                return

            # إنشاء التطبيق
            self.application = Application.builder().token(self.bot_token).build()

            # إعداد المعالجات
            self.setup_handlers()

            print("🤖 بدء تشغيل بوت تيليجرام المحسن...")
            print(f"🔗 رابط البوت: https://t.me/{self.bot_token.split(':')[0]}")

            # بدء التشغيل
            await self.application.run_polling(drop_pending_updates=True)

        except Exception as e:
            print(f"❌ فشل في تشغيل البوت: {e}")
            traceback.print_exc()

    def start_bot_sync(self):
        """بدء تشغيل البوت بطريقة متزامنة"""
        try:
            if not self.bot_token:
                print("❌ لا يوجد توكن تيليجرام في الإعدادات")
                return

            # إنشاء التطبيق
            self.application = Application.builder().token(self.bot_token).build()

            # إعداد المعالجات
            self.setup_handlers()

            print("🤖 بدء تشغيل بوت تيليجرام المحسن...")
            print(f"🔗 رابط البوت: https://t.me/{self.bot_token.split(':')[0]}")

            # بدء التشغيل بطريقة متزامنة
            self.application.run_polling(drop_pending_updates=True)

        except Exception as e:
            print(f"❌ فشل في تشغيل البوت: {e}")
            traceback.print_exc()

# إنشاء مثيل البوت
enhanced_telegram_bot = EnhancedTelegramBot()

async def main():
    """الدالة الرئيسية"""
    await enhanced_telegram_bot.start_bot()

if __name__ == "__main__":
    asyncio.run(main())
