#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وكيل أخبار الألعاب - النسخة المحسنة للاستضافة المجانية
Gaming News Agent - Optimized for Free Hosting (512MB RAM)

تم تحسينه للعمل مع:
- 512MB RAM
- 0.1 CPU
- APIs خارجية للذكاء الاصطناعي
- معالجة خفيفة محلياً
"""

import asyncio
import signal
import sys
import os
import gc
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import traceback

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد النشر للاستضافة المجانية
from deployment_config import setup_deployment
setup_deployment()

# الوحدات الأساسية فقط (خفيفة)
from modules.logger import logger
from modules.database import db
from modules.content_scraper_lite import get_content_scraper
from modules.content_generator import ContentGenerator
from modules.publisher import PublisherManager
from modules.web_approval_system import web_approval_system
from modules.simple_backup import simple_backup
from modules.error_handler import health_monitor, error_recovery, scheduler
from modules.memory_manager_lite import memory_manager, start_memory_monitoring

# إعدادات النظام
from config.settings import BotConfig

# متغيرات النظام المحسن
LITE_MODE = True  # تفعيل الوضع الخفيف
MAX_MEMORY_MB = 400  # الحد الأقصى للذاكرة (400MB من 512MB)
CLEANUP_INTERVAL = 300  # تنظيف الذاكرة كل 5 دقائق

class GamingNewsAgentLite:
    """وكيل أخبار الألعاب - النسخة المحسنة للاستضافة المجانية"""
    
    def __init__(self):
        self.is_running = False
        self.startup_time = datetime.now()
        self.processed_articles_count = 0
        self.web_interface_opened = False
        self.web_server_thread = None
        
        # الوحدات الأساسية فقط
        self.scraper = None
        self.content_generator = None
        self.publisher = None

        # تسجيل وظائف التنظيف المخصصة
        memory_manager.register_cleanup_function(self._cleanup_agent_cache, "Agent Cache")

        # بدء مراقبة الذاكرة المتقدمة
        start_memory_monitoring()
        
        # بدء خادم الواجهة الويب الخفيف
        self.start_web_server()
        
        logger.info("🚀 تم تهيئة وكيل أخبار الألعاب - النسخة المحسنة")

    def _cleanup_agent_cache(self):
        """تنظيف كاش الوكيل المخصص"""
        try:
            # تنظيف كاش المكونات
            if hasattr(self, 'scraper') and self.scraper:
                if hasattr(self.scraper, 'clear_cache'):
                    self.scraper.clear_cache()

            if hasattr(self, 'content_generator') and self.content_generator:
                if hasattr(self.content_generator, 'clear_cache'):
                    self.content_generator.clear_cache()

            logger.debug("🧹 تم تنظيف كاش الوكيل")

        except Exception as e:
            logger.warning(f"خطأ في تنظيف كاش الوكيل: {e}")
    
    def start_web_server(self):
        """بدء خادم الواجهة الويب الخفيف"""
        def run_web_server():
            try:
                from web_api import app
                port = int(os.getenv('PORT', 5000))
                host = os.getenv('HOST', '0.0.0.0')
                
                # تشغيل خفيف للخادم
                app.run(
                    host=host,
                    port=port,
                    debug=False,
                    threaded=True,
                    use_reloader=False
                )
            except Exception as e:
                logger.error(f"خطأ في تشغيل خادم الويب: {e}")
        
        if not self.web_server_thread:
            self.web_server_thread = threading.Thread(target=run_web_server, daemon=True)
            self.web_server_thread.start()
            logger.info("🌐 تم بدء خادم الواجهة الويب")
    
    async def initialize_components(self):
        """تهيئة المكونات الأساسية فقط (lazy loading)"""
        try:
            if not self.scraper:
                self.scraper = get_content_scraper()
                logger.info("✅ تم تهيئة مستخرج المحتوى")

            if not self.content_generator:
                self.content_generator = ContentGenerator()
                logger.info("✅ تم تهيئة مولد المحتوى")

            if not self.publisher:
                self.publisher = PublisherManager()
                logger.info("✅ تم تهيئة ناشر المحتوى")

            # تنظيف الذاكرة بعد التهيئة
            memory_manager.cleanup_memory()

        except Exception as e:
            logger.error(f"خطأ في تهيئة المكونات: {e}")
            raise
    
    async def process_single_article(self):
        """معالجة مقال واحد (محسن للذاكرة)"""
        try:
            # تهيئة المكونات إذا لم تكن مهيأة
            await self.initialize_components()
            
            # استخراج المحتوى
            logger.info("🔍 بحث عن محتوى جديد...")
            source_content = await self.scraper.get_latest_content()
            
            if not source_content:
                logger.info("ℹ️ لا يوجد محتوى جديد")
                return False
            
            # توليد المقال
            logger.info("✍️ توليد المقال...")
            article = self.content_generator.generate_article(
                source_content, 
                content_type="gaming_news"
            )
            
            if not article:
                logger.warning("⚠️ فشل في توليد المقال")
                return False
            
            # نشر المقال
            logger.info("📤 نشر المقال...")
            success = await self.publisher.publish_article(article)
            
            if success:
                self.processed_articles_count += 1
                logger.info(f"✅ تم نشر المقال بنجاح - العدد الإجمالي: {self.processed_articles_count}")
                
                # تنظيف الذاكرة بعد كل مقال
                memory_manager.cleanup_memory()
                return True
            else:
                logger.error("❌ فشل في نشر المقال")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في معالجة المقال: {e}")
            return False
    
    async def run_continuous(self):
        """تشغيل مستمر محسن للذاكرة"""
        self.is_running = True
        logger.info("🚀 بدء التشغيل المستمر للوكيل")
        
        while self.is_running:
            try:
                # معالجة مقال واحد
                await self.process_single_article()
                
                # انتظار قبل المعالجة التالية
                await asyncio.sleep(1800)  # 30 دقيقة
                
            except KeyboardInterrupt:
                logger.info("⏹️ تم إيقاف الوكيل بواسطة المستخدم")
                break
            except Exception as e:
                logger.error(f"خطأ في التشغيل المستمر: {e}")
                await asyncio.sleep(300)  # انتظار 5 دقائق عند الخطأ
    
    def stop(self):
        """إيقاف الوكيل"""
        self.is_running = False
        logger.info("⏹️ تم إيقاف الوكيل")
    
    def get_status(self):
        """الحصول على حالة الوكيل"""
        uptime = datetime.now() - self.startup_time
        memory_stats = memory_manager.get_stats()

        return {
            'status': 'running' if self.is_running else 'stopped',
            'uptime': str(uptime).split('.')[0],
            'articles_processed': self.processed_articles_count,
            'memory_usage_mb': memory_stats['current_usage_mb'],
            'peak_memory_mb': memory_stats['peak_usage_mb'],
            'memory_cleanups': memory_stats['cleanup_count'],
            'last_cleanup': memory_stats['last_cleanup'].strftime('%H:%M:%S'),
            'memory_usage_percent': memory_stats['usage_percent']
        }

# إنشاء مثيل الوكيل
agent = GamingNewsAgentLite()

def signal_handler(signum, frame):
    """معالج إشارات النظام"""
    logger.info("🛑 تم استلام إشارة الإيقاف")
    agent.stop()
    sys.exit(0)

# تسجيل معالجات الإشارات
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🎮 بدء تشغيل وكيل أخبار الألعاب - النسخة المحسنة")
        logger.info(f"💾 الحد الأقصى للذاكرة: {MAX_MEMORY_MB}MB")
        
        # تشغيل الوكيل
        await agent.run_continuous()
        
    except Exception as e:
        logger.error(f"خطأ في التشغيل الرئيسي: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    # تشغيل الوكيل
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 تم إنهاء البرنامج")
    except Exception as e:
        logger.error(f"خطأ فادح: {e}")
        sys.exit(1)
