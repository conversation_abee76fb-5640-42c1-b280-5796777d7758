#!/usr/bin/env python3
"""
نظام إدارة مفاتيح API عبر تيليجرام
"""

import os
import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import asyncio

@dataclass
class ApiKey:
    """فئة مفتاح API"""
    id: int
    name: str
    key: str
    service: str
    status: str  # active, inactive, expired
    created_at: str
    last_used: Optional[str] = None
    usage_count: int = 0
    description: Optional[str] = None

class TelegramApiManager:
    """مدير مفاتيح API عبر تيليجرام"""
    
    def __init__(self, db_path: str = "data/api_keys.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    key TEXT NOT NULL,
                    service TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    last_used TEXT,
                    usage_count INTEGER DEFAULT 0,
                    description TEXT
                )
            """)

            # التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(api_keys)")
            columns = [column[1] for column in cursor.fetchall()]

            required_columns = {
                'name': 'TEXT NOT NULL',
                'key': 'TEXT NOT NULL',
                'service': 'TEXT NOT NULL',
                'status': 'TEXT DEFAULT "active"',
                'created_at': 'TEXT DEFAULT CURRENT_TIMESTAMP',
                'last_used': 'TEXT',
                'usage_count': 'INTEGER DEFAULT 0',
                'description': 'TEXT'
            }

            for column_name, column_def in required_columns.items():
                if column_name not in columns:
                    try:
                        conn.execute(f"ALTER TABLE api_keys ADD COLUMN {column_name} {column_def}")
                        print(f"تم إضافة العمود: {column_name}")
                    except Exception as e:
                        print(f"خطأ في إضافة العمود {column_name}: {e}")
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_usage_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_key_id INTEGER,
                    service TEXT,
                    endpoint TEXT,
                    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN,
                    error_message TEXT,
                    FOREIGN KEY (api_key_id) REFERENCES api_keys (id)
                )
            """)
            
            # إضافة المفاتيح الموجودة من .env إذا لم تكن موجودة
            self._import_env_keys()
    
    def _import_env_keys(self):
        """استيراد المفاتيح من ملف .env"""
        from dotenv import load_dotenv
        load_dotenv()
        
        # قائمة المفاتيح المهمة من .env
        env_keys = {
            "GEMINI_API_KEY": "Google Gemini",
            "TELEGRAM_BOT_TOKEN": "Telegram Bot",
            "BLOGGER_CLIENT_ID": "Google Blogger",
            "BLOGGER_CLIENT_SECRET": "Google Blogger Secret",
            "FREEPIK_API_KEY": "Freepik",
            "FLUXAI_API_KEY": "FluxAI",
            "RAWG_API_KEY": "RAWG Gaming",
            "APIFY_API_TOKEN": "Apify",
            "SEARCH1API_KEY_1": "Search1API",
            "SEARCH1API_KEY_2": "Search1API",
            "SEARCH1API_KEY_3": "Search1API",
            "TAVILY_API_KEY_1": "Tavily Search",
            "TAVILY_API_KEY_2": "Tavily Search",
            "SERPAPI_KEY_1": "SerpAPI",
            "ASSEMBLYAI_API_KEY_1": "AssemblyAI",
            "ASSEMBLYAI_API_KEY_2": "AssemblyAI",
            "WIT_AI_ACCESS_TOKEN_1": "Wit.ai",
            "YOUTUBE_DATA_API_KEY_1": "YouTube Data",
            "PEXELS_API_KEY_1": "Pexels Images"
        }
        
        with sqlite3.connect(self.db_path) as conn:
            for env_key, service in env_keys.items():
                key_value = os.getenv(env_key)
                if key_value and key_value != "your_key_here":
                    # التحقق من وجود المفتاح
                    existing = conn.execute(
                        "SELECT id FROM api_keys WHERE key = ?", (key_value,)
                    ).fetchone()
                    
                    if not existing:
                        conn.execute("""
                            INSERT INTO api_keys (name, key, service, description)
                            VALUES (?, ?, ?, ?)
                        """, (env_key, key_value, service, f"مستورد من ملف .env"))
    
    def get_all_keys(self) -> List[ApiKey]:
        """الحصول على جميع المفاتيح"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            rows = conn.execute("""
                SELECT * FROM api_keys ORDER BY service, name
            """).fetchall()
            
            return [ApiKey(**dict(row)) for row in rows]
    
    def get_keys_by_service(self, service: str) -> List[ApiKey]:
        """الحصول على المفاتيح حسب الخدمة"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            rows = conn.execute("""
                SELECT * FROM api_keys WHERE service = ? ORDER BY name
            """, (service,)).fetchall()
            
            return [ApiKey(**dict(row)) for row in rows]
    
    def add_key(self, name: str, key: str, service: str, description: str = "") -> bool:
        """إضافة مفتاح جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO api_keys (name, key, service, description)
                    VALUES (?, ?, ?, ?)
                """, (name, key, service, description))
            return True
        except Exception as e:
            print(f"خطأ في إضافة المفتاح: {e}")
            return False
    
    def update_key(self, key_id: int, **kwargs) -> bool:
        """تحديث مفتاح موجود"""
        try:
            allowed_fields = ['name', 'key', 'service', 'status', 'description']
            updates = []
            values = []
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    updates.append(f"{field} = ?")
                    values.append(value)
            
            if not updates:
                return False
            
            values.append(key_id)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute(f"""
                    UPDATE api_keys SET {', '.join(updates)} WHERE id = ?
                """, values)
            return True
        except Exception as e:
            print(f"خطأ في تحديث المفتاح: {e}")
            return False
    
    def delete_key(self, key_id: int) -> bool:
        """حذف مفتاح"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM api_keys WHERE id = ?", (key_id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف المفتاح: {e}")
            return False
    
    def get_services_list(self) -> List[str]:
        """الحصول على قائمة الخدمات"""
        with sqlite3.connect(self.db_path) as conn:
            rows = conn.execute("""
                SELECT DISTINCT service FROM api_keys ORDER BY service
            """).fetchall()
            
            return [row[0] for row in rows]
    
    def log_usage(self, api_key_id: int, service: str, endpoint: str, 
                  success: bool, error_message: str = None):
        """تسجيل استخدام المفتاح"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # تسجيل الاستخدام
                conn.execute("""
                    INSERT INTO api_usage_log (api_key_id, service, endpoint, success, error_message)
                    VALUES (?, ?, ?, ?, ?)
                """, (api_key_id, service, endpoint, success, error_message))
                
                # تحديث عداد الاستخدام
                conn.execute("""
                    UPDATE api_keys SET 
                        usage_count = usage_count + 1,
                        last_used = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (api_key_id,))
        except Exception as e:
            print(f"خطأ في تسجيل الاستخدام: {e}")
    
    def get_usage_stats(self, days: int = 7) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        with sqlite3.connect(self.db_path) as conn:
            # إجمالي المفاتيح
            total_keys = conn.execute("SELECT COUNT(*) FROM api_keys").fetchone()[0]
            
            # المفاتيح النشطة
            active_keys = conn.execute(
                "SELECT COUNT(*) FROM api_keys WHERE status = 'active'"
            ).fetchone()[0]
            
            # الاستخدام في الأيام الأخيرة
            recent_usage = conn.execute("""
                SELECT COUNT(*) FROM api_usage_log 
                WHERE timestamp >= datetime('now', '-{} days')
            """.format(days)).fetchone()[0]
            
            # أكثر الخدمات استخداماً
            top_services = conn.execute("""
                SELECT service, COUNT(*) as usage_count
                FROM api_usage_log 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY service 
                ORDER BY usage_count DESC 
                LIMIT 5
            """.format(days)).fetchall()
            
            return {
                "total_keys": total_keys,
                "active_keys": active_keys,
                "recent_usage": recent_usage,
                "top_services": top_services,
                "days": days
            }
    
    def export_keys(self) -> str:
        """تصدير المفاتيح إلى JSON"""
        keys = self.get_all_keys()
        export_data = []
        
        for key in keys:
            export_data.append({
                "name": key.name,
                "service": key.service,
                "status": key.status,
                "description": key.description,
                "created_at": key.created_at,
                "usage_count": key.usage_count
                # لا نصدر المفتاح الفعلي لأسباب أمنية
            })
        
        return json.dumps(export_data, ensure_ascii=False, indent=2)

# إنشاء مثيل عام
telegram_api_manager = TelegramApiManager()
