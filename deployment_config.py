#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات النشر للاستضافة
Deployment Configuration for Hosting Platforms
"""

import os
import sys
from pathlib import Path

class DeploymentConfig:
    """إعدادات النشر للمنصات المختلفة"""
    
    def __init__(self):
        self.platform = self.detect_platform()
        self.setup_environment()
    
    def detect_platform(self):
        """كشف منصة الاستضافة"""
        if os.getenv('RENDER'):
            return 'render'
        elif os.getenv('HEROKU'):
            return 'heroku'
        elif os.getenv('RAILWAY_ENVIRONMENT'):
            return 'railway'
        elif os.getenv('VERCEL'):
            return 'vercel'
        elif os.getenv('NETLIFY'):
            return 'netlify'
        else:
            return 'local'
    
    def setup_environment(self):
        """إعداد البيئة حسب المنصة"""
        
        # إعدادات عامة
        default_env = {
            'PYTHONUNBUFFERED': '1',
            'PYTHONPATH': str(Path(__file__).parent),
            'TZ': 'UTC'
        }
        
        # إعدادات خاصة بكل منصة
        platform_configs = {
            'render': {
                'HOST': '0.0.0.0',
                'PORT': '10000',
                'DEBUG': 'false',
                'LOG_LEVEL': 'INFO'
            },
            'heroku': {
                'HOST': '0.0.0.0',
                'PORT': os.getenv('PORT', '5000'),
                'DEBUG': 'false',
                'LOG_LEVEL': 'INFO'
            },
            'railway': {
                'HOST': '0.0.0.0',
                'PORT': os.getenv('PORT', '8080'),
                'DEBUG': 'false',
                'LOG_LEVEL': 'INFO'
            },
            'local': {
                'HOST': 'localhost',
                'PORT': '5000',
                'DEBUG': 'true',
                'LOG_LEVEL': 'DEBUG'
            }
        }
        
        # تطبيق الإعدادات
        config = platform_configs.get(self.platform, platform_configs['local'])
        
        for key, value in {**default_env, **config}.items():
            if key not in os.environ:
                os.environ[key] = str(value)
    
    def get_database_url(self):
        """الحصول على رابط قاعدة البيانات"""
        if self.platform in ['render', 'heroku', 'railway']:
            # استخدام SQLite للمنصات السحابية
            return 'sqlite:///data/articles.db'
        else:
            return 'sqlite:///data/articles.db'
    
    def get_static_files_config(self):
        """إعدادات الملفات الثابتة"""
        return {
            'static_folder': 'web_interface',
            'static_url_path': '/static'
        }
    
    def get_logging_config(self):
        """إعدادات التسجيل"""
        log_level = os.getenv('LOG_LEVEL', 'INFO')
        
        return {
            'level': log_level,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'handlers': ['console'] if self.platform != 'local' else ['console', 'file']
        }
    
    def create_required_directories(self):
        """إنشاء المجلدات المطلوبة"""
        required_dirs = [
            'data',
            'logs', 
            'cache',
            'images',
            'temp',
            'config'
        ]
        
        for dir_name in required_dirs:
            Path(dir_name).mkdir(exist_ok=True)
    
    def get_security_config(self):
        """إعدادات الأمان"""
        return {
            'secret_key': os.getenv('SECRET_KEY', 'gaming_news_agent_2025'),
            'max_content_length': 16 * 1024 * 1024,  # 16MB
            'session_timeout': 3600,  # ساعة واحدة
            'rate_limit': {
                'enabled': self.platform != 'local',
                'requests_per_minute': 100
            }
        }
    
    def get_performance_config(self):
        """إعدادات الأداء محسنة للاستضافة المجانية"""
        lite_mode = os.getenv('LITE_MODE', 'false').lower() == 'true'

        if lite_mode:
            return {
                'max_workers': 1,  # عامل واحد فقط لتوفير الذاكرة
                'timeout': 60,    # مهلة أطول للعمليات البطيئة
                'keep_alive': False,  # عدم الاحتفاظ بالاتصالات
                'compression': True,
                'memory_limit_mb': int(os.getenv('MAX_MEMORY_MB', '400')),
                'cleanup_interval': 300,  # تنظيف كل 5 دقائق
                'lazy_loading': True
            }
        else:
            return {
                'max_workers': 4 if self.platform in ['render', 'heroku'] else 2,
                'timeout': 30,
                'keep_alive': True,
                'compression': True
            }
    
    def is_production(self):
        """فحص إذا كان في بيئة الإنتاج"""
        return self.platform != 'local'
    
    def get_cors_config(self):
        """إعدادات CORS"""
        if self.is_production():
            return {
                'origins': ['*'],  # يمكن تخصيصها حسب الحاجة
                'methods': ['GET', 'POST', 'PUT', 'DELETE'],
                'allow_headers': ['Content-Type', 'Authorization']
            }
        else:
            return {'origins': '*'}

# إنشاء مثيل الإعدادات
deployment_config = DeploymentConfig()

def setup_deployment():
    """إعداد النشر"""
    try:
        # إنشاء المجلدات المطلوبة
        deployment_config.create_required_directories()
        
        # طباعة معلومات المنصة
        print(f"🚀 تشغيل على منصة: {deployment_config.platform}")
        print(f"🌐 المضيف: {os.getenv('HOST', 'localhost')}")
        print(f"🔌 المنفذ: {os.getenv('PORT', '5000')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد النشر: {e}")
        return False

if __name__ == "__main__":
    setup_deployment()
