#!/usr/bin/env python3
"""
تشغيل البوت فقط بدون الوكيل - الإصدار الجديد
"""

import asyncio
import signal
import sys
import os
import threading
import time
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد النشر
from deployment_config import setup_deployment
setup_deployment()

from modules.logger import logger
from modules.enhanced_telegram_bot import enhanced_telegram_bot
from modules.agent_data_manager import agent_data_manager
from config.settings import BotConfig

class BotOnlyManager:
    """مدير تشغيل البوت فقط"""
    
    def __init__(self):
        self.is_running = False
        self.bot_thread = None
        self.startup_time = datetime.now()
        
        # معلومات النظام
        self.system_info = {
            "mode": "bot_only",
            "agent_status": "stopped",
            "bot_status": "stopped",
            "startup_time": self.startup_time.isoformat()
        }
    
    def setup_environment(self):
        """إعداد البيئة الأساسية"""
        try:
            # إنشاء المجلدات الأساسية
            essential_dirs = ["data", "logs", "config", "backups"]
            for dir_name in essential_dirs:
                os.makedirs(dir_name, exist_ok=True)
            
            # التحقق من الإعدادات الأساسية
            if not BotConfig.TELEGRAM_BOT_TOKEN:
                logger.error("❌ لا يوجد توكن تيليجرام في الإعدادات")
                return False
            
            logger.info("✅ تم إعداد البيئة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إعداد البيئة: {e}")
            return False
    
    def start_bot_only(self):
        """تشغيل البوت فقط"""
        try:
            self.is_running = True
            self.system_info["bot_status"] = "starting"

            logger.info("🤖 بدء تشغيل بوت تيليجرام فقط...")
            logger.info("📋 الوكيل متوقف - يمكن تشغيله من البوت")

            # تحديث حالة النظام
            self.system_info["bot_status"] = "running"
            self.system_info["agent_status"] = "stopped"

            # تشغيل البوت بطريقة متزامنة
            enhanced_telegram_bot.start_bot_sync()

        except Exception as e:
            logger.error(f"❌ فشل في تشغيل البوت: {e}")
            self.system_info["bot_status"] = "error"
            raise
    
    def get_system_status(self):
        """الحصول على حالة النظام"""
        return {
            **self.system_info,
            "uptime_seconds": (datetime.now() - self.startup_time).total_seconds(),
            "data_summary": agent_data_manager.get_data_summary()
        }
    
    async def graceful_shutdown(self):
        """إيقاف النظام بأمان"""
        try:
            logger.info("⏹️ بدء إيقاف النظام...")
            self.is_running = False
            self.system_info["bot_status"] = "stopping"
            
            # حفظ حالة النظام
            self._save_system_state()
            
            logger.info("✅ تم إيقاف النظام بأمان")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف النظام: {e}")
    
    def _save_system_state(self):
        """حفظ حالة النظام"""
        try:
            state_file = "data/system_state.json"
            state_data = {
                **self.system_info,
                "last_shutdown": datetime.now().isoformat()
            }
            
            import json
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.warning(f"⚠️ فشل في حفظ حالة النظام: {e}")

def main():
    """الدالة الرئيسية"""
    bot_manager = BotOnlyManager()

    try:
        # إعداد البيئة
        if not bot_manager.setup_environment():
            logger.error("❌ فشل في إعداد البيئة")
            sys.exit(1)

        # إعداد معالج الإيقاف
        def signal_handler(signum, frame):
            logger.info("⌨️ تم طلب الإيقاف")
            bot_manager.is_running = False
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # تشغيل البوت فقط
        bot_manager.start_bot_only()

    except KeyboardInterrupt:
        logger.info("⌨️ تم الإيقاف بواسطة المستخدم")
    except Exception as e:
        logger.critical(f"❌ خطأ حرج: {e}")
        sys.exit(1)
    finally:
        bot_manager.is_running = False

if __name__ == "__main__":
    try:
        # رسالة ترحيب
        print("=" * 60)
        print("🤖 بوت تيليجرام لإدارة وكيل أخبار الألعاب")
        print("=" * 60)
        print("📋 الوضع: البوت فقط (الوكيل متوقف)")
        print("🎮 يمكن تشغيل الوكيل من خلال البوت")
        print("🔗 ابحث عن البوت في تيليجرام وأرسل /start")
        print("=" * 60)
        
        # تشغيل النظام
        main()
        
    except KeyboardInterrupt:
        print("\n⌨️ تم الإيقاف بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
