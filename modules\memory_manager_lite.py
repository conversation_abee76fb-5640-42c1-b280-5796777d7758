#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الذاكرة المحسن للوضع الخفيف
Memory Manager for Lite Mode - Free Hosting Optimization
"""

import gc
import os
import sys
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import traceback

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

try:
    from .logger import logger
    from config.lite_mode_config import lite_config
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

class MemoryManagerLite:
    """مدير الذاكرة المحسن للاستضافة المجانية"""
    
    def __init__(self):
        self.max_memory_mb = int(os.getenv('MAX_MEMORY_MB', '400'))
        self.warning_threshold = self.max_memory_mb * 0.8  # 80%
        self.critical_threshold = self.max_memory_mb * 0.9  # 90%
        self.cleanup_interval = 300  # 5 دقائق
        
        # إحصائيات الذاكرة
        self.stats = {
            'current_usage_mb': 0,
            'peak_usage_mb': 0,
            'cleanup_count': 0,
            'warning_count': 0,
            'critical_count': 0,
            'last_cleanup': datetime.now(),
            'start_time': datetime.now()
        }
        
        # قائمة وظائف التنظيف المسجلة
        self.cleanup_functions = []
        
        # حالة المراقبة
        self.monitoring = False
        self.monitor_thread = None
        
        # تسجيل وظائف التنظيف الأساسية
        self._register_default_cleanups()
        
        logger.info(f"🧠 تم تهيئة مدير الذاكرة - الحد الأقصى: {self.max_memory_mb}MB")
    
    def _register_default_cleanups(self):
        """تسجيل وظائف التنظيف الافتراضية"""
        self.register_cleanup_function(self._gc_collect, "Python Garbage Collection")
        self.register_cleanup_function(self._clear_import_cache, "Import Cache")
        self.register_cleanup_function(self._clear_sys_modules_cache, "Sys Modules Cache")
    
    def register_cleanup_function(self, func: Callable, name: str):
        """تسجيل وظيفة تنظيف جديدة"""
        self.cleanup_functions.append({
            'function': func,
            'name': name,
            'last_run': None,
            'run_count': 0,
            'error_count': 0
        })
        logger.debug(f"📝 تم تسجيل وظيفة التنظيف: {name}")
    
    def get_memory_usage(self) -> float:
        """الحصول على استهلاك الذاكرة الحالي بالـ MB"""
        if HAS_PSUTIL:
            try:
                process = psutil.Process()
                return process.memory_info().rss / 1024 / 1024
            except Exception:
                pass
        
        # طريقة بديلة تقريبية
        try:
            import resource
            return resource.getrusage(resource.RUSAGE_SELF).ru_maxrss / 1024
        except Exception:
            return 0
    
    def _gc_collect(self):
        """تنظيف Python garbage collector"""
        try:
            collected = gc.collect()
            logger.debug(f"🗑️ تم تحرير {collected} كائن من الذاكرة")
            return collected
        except Exception as e:
            logger.warning(f"خطأ في garbage collection: {e}")
            return 0
    
    def _clear_import_cache(self):
        """تنظيف كاش الاستيراد"""
        try:
            if hasattr(sys, '_getframe'):
                # تنظيف كاش الوحدات المستوردة
                import importlib
                if hasattr(importlib, 'invalidate_caches'):
                    importlib.invalidate_caches()
                    logger.debug("🧹 تم تنظيف كاش الاستيراد")
        except Exception as e:
            logger.warning(f"خطأ في تنظيف كاش الاستيراد: {e}")
    
    def _clear_sys_modules_cache(self):
        """تنظيف كاش sys.modules (حذر)"""
        try:
            # إزالة الوحدات غير المستخدمة (بحذر شديد)
            modules_to_remove = []
            for module_name in sys.modules:
                if module_name.startswith('__pycache__'):
                    modules_to_remove.append(module_name)
            
            for module_name in modules_to_remove:
                try:
                    del sys.modules[module_name]
                except Exception:
                    pass
            
            if modules_to_remove:
                logger.debug(f"🧹 تم إزالة {len(modules_to_remove)} وحدة من الكاش")
                
        except Exception as e:
            logger.warning(f"خطأ في تنظيف sys.modules: {e}")
    
    def cleanup_memory(self, force: bool = False) -> Dict:
        """تنظيف الذاكرة"""
        start_time = time.time()
        initial_memory = self.get_memory_usage()
        
        cleanup_results = {
            'initial_memory_mb': initial_memory,
            'final_memory_mb': 0,
            'freed_mb': 0,
            'functions_run': 0,
            'functions_failed': 0,
            'duration_seconds': 0
        }
        
        try:
            logger.info("🧹 بدء تنظيف الذاكرة...")
            
            # تشغيل وظائف التنظيف
            for cleanup_func in self.cleanup_functions:
                try:
                    logger.debug(f"🔧 تشغيل: {cleanup_func['name']}")
                    cleanup_func['function']()
                    cleanup_func['last_run'] = datetime.now()
                    cleanup_func['run_count'] += 1
                    cleanup_results['functions_run'] += 1
                    
                except Exception as e:
                    logger.warning(f"خطأ في {cleanup_func['name']}: {e}")
                    cleanup_func['error_count'] += 1
                    cleanup_results['functions_failed'] += 1
            
            # قياس النتائج
            final_memory = self.get_memory_usage()
            freed_memory = max(0, initial_memory - final_memory)
            duration = time.time() - start_time
            
            cleanup_results.update({
                'final_memory_mb': final_memory,
                'freed_mb': freed_memory,
                'duration_seconds': duration
            })
            
            # تحديث الإحصائيات
            self.stats['cleanup_count'] += 1
            self.stats['last_cleanup'] = datetime.now()
            self.stats['current_usage_mb'] = final_memory
            
            if final_memory > self.stats['peak_usage_mb']:
                self.stats['peak_usage_mb'] = final_memory
            
            logger.info(f"✅ تم تنظيف الذاكرة - تحرير {freed_memory:.1f}MB في {duration:.2f}s")
            
            return cleanup_results
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف الذاكرة: {e}")
            cleanup_results['error'] = str(e)
            return cleanup_results
    
    def check_memory_status(self) -> Dict:
        """فحص حالة الذاكرة"""
        current_memory = self.get_memory_usage()
        self.stats['current_usage_mb'] = current_memory
        
        status = {
            'current_mb': current_memory,
            'max_mb': self.max_memory_mb,
            'usage_percent': (current_memory / self.max_memory_mb) * 100,
            'status': 'normal',
            'action_needed': False
        }
        
        if current_memory >= self.critical_threshold:
            status['status'] = 'critical'
            status['action_needed'] = True
            self.stats['critical_count'] += 1
            logger.warning(f"🚨 استهلاك ذاكرة حرج: {current_memory:.1f}MB")
            
        elif current_memory >= self.warning_threshold:
            status['status'] = 'warning'
            status['action_needed'] = True
            self.stats['warning_count'] += 1
            logger.warning(f"⚠️ استهلاك ذاكرة مرتفع: {current_memory:.1f}MB")
        
        return status
    
    def start_monitoring(self):
        """بدء مراقبة الذاكرة"""
        if self.monitoring:
            return
        
        self.monitoring = True
        
        def monitor_loop():
            while self.monitoring:
                try:
                    status = self.check_memory_status()
                    
                    if status['action_needed']:
                        self.cleanup_memory()
                    
                    time.sleep(self.cleanup_interval)
                    
                except Exception as e:
                    logger.error(f"خطأ في مراقبة الذاكرة: {e}")
                    time.sleep(60)  # انتظار دقيقة عند الخطأ
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("👁️ تم بدء مراقبة الذاكرة")
    
    def stop_monitoring(self):
        """إيقاف مراقبة الذاكرة"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("⏹️ تم إيقاف مراقبة الذاكرة")
    
    def get_stats(self) -> Dict:
        """الحصول على إحصائيات الذاكرة"""
        uptime = datetime.now() - self.stats['start_time']
        current_memory = self.get_memory_usage()
        
        return {
            **self.stats,
            'current_usage_mb': current_memory,
            'max_memory_mb': self.max_memory_mb,
            'usage_percent': (current_memory / self.max_memory_mb) * 100,
            'uptime_seconds': uptime.total_seconds(),
            'cleanup_functions_count': len(self.cleanup_functions),
            'monitoring_active': self.monitoring
        }
    
    def force_emergency_cleanup(self):
        """تنظيف طوارئ قوي"""
        logger.warning("🚨 تنظيف طوارئ للذاكرة!")
        
        try:
            # تنظيف قوي متعدد المراحل
            for i in range(3):
                self.cleanup_memory(force=True)
                time.sleep(1)
            
            # إجبار Python على تحرير الذاكرة
            gc.set_threshold(0)  # تعطيل GC مؤقتاً
            gc.collect()
            gc.set_threshold(700, 10, 10)  # إعادة تفعيل GC
            
            logger.info("✅ تم تنظيف الطوارئ")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف الطوارئ: {e}")

# إنشاء مثيل مدير الذاكرة
memory_manager = MemoryManagerLite()

def get_memory_manager():
    """الحصول على مدير الذاكرة"""
    return memory_manager

def cleanup_memory():
    """تنظيف الذاكرة (دالة مساعدة)"""
    return memory_manager.cleanup_memory()

def start_memory_monitoring():
    """بدء مراقبة الذاكرة (دالة مساعدة)"""
    memory_manager.start_monitoring()

if __name__ == "__main__":
    # اختبار مدير الذاكرة
    print("🧠 اختبار مدير الذاكرة")
    
    # عرض الحالة الحالية
    stats = memory_manager.get_stats()
    print(f"الذاكرة الحالية: {stats['current_usage_mb']:.1f}MB")
    
    # تنظيف تجريبي
    result = memory_manager.cleanup_memory()
    print(f"تم تحرير: {result['freed_mb']:.1f}MB")
    
    # بدء المراقبة لمدة 10 ثوان
    memory_manager.start_monitoring()
    time.sleep(10)
    memory_manager.stop_monitoring()
    
    print("✅ انتهى الاختبار")
